# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a commercial-grade WAN client based on SDWAN ZZVPN protocol, built with a modern modular architecture. The project consists of:
- **Go backend service**: Core VPN functionality and API server
- **Flutter frontend**: Cross-platform UI for Windows, macOS, iOS, and Android
- **Platform-specific implementations**: Native code for VPN services on each platform

## Common Development Commands

### Building the Project

**Windows/Linux (Go backend + Flutter)**:
```bash
# Build all components (backend + frontend + installer)
make build

# Build backend only
make backend

# Build frontend only  
make frontend

# Debug mode build
make debug

# Run in development mode
make run
```

**Flutter Commands**:
```bash
# Get dependencies
cd ui/flutter
flutter pub get

# Run app in development
flutter run -d windows    # Windows
flutter run -d macos      # macOS
flutter run -d ios        # iOS
flutter run -d android    # Android

# Build release versions
flutter build windows --release
flutter build macos --release
flutter build ios --release
flutter build apk --release

# Generate localization files
flutter gen-l10n

# Run tests
flutter test
```

**iOS/macOS specific**:
```bash
# iOS - Update pods
cd ui/flutter/ios
pod install

# macOS - Update pods
cd ui/flutter/macos
pod install

# Run via Xcode
open ios/Runner.xcworkspace
open macos/Runner.xcworkspace
```

### Testing

**Flutter tests**:
```bash
cd ui/flutter
flutter test
flutter test test/models/
flutter test test/services/
```

**Go tests**: Currently no standard Go test suite. Consider adding `go test ./...` support.

### Code Quality

**Flutter linting**:
```bash
cd ui/flutter
flutter analyze
```

**Go linting**: No standard linter configured. Consider adding golangci-lint.

## High-Level Architecture

### Backend Architecture (Go)

The Go backend follows a clean, layered architecture:

1. **Platform Layer** (`internal/platform/`): Abstracts OS-specific operations
   - TUN device management for VPN traffic
   - Network interface configuration
   - Windows service integration

2. **Protocol Layer** (`internal/protocol/`): SDWAN ZZVPN protocol implementation
   - Packet encoding/decoding with TLV attributes
   - AES and XOR encryption
   - Authentication and heartbeat management

3. **Connection Layer** (`internal/connection/`): VPN lifecycle management
   - State machine for connection states (Disconnected → Connecting → Connected)
   - Server selection with latency-based routing
   - Automatic reconnection and failover
   - Network interface monitoring (10-second intervals)

4. **Service Layer** (`internal/service/`): API and communication
   - HTTP REST API on port 56544
   - WebSocket for real-time status updates
   - Configuration management with YAML files

5. **Infrastructure Layer** (`internal/common/`): Shared utilities
   - Structured logging with module-based loggers
   - Unified error handling with error codes
   - Performance monitoring and metrics
   - Object pools and goroutine management

### Frontend Architecture (Flutter)

The Flutter app uses a service-oriented architecture:

1. **Platform Channels**: Communication with native code
   - Method Channel: `com.panabit.client/vpn_service` 
   - Event Channel: `com.panabit.client/vpn_events`

2. **Service Layer** (`lib/services/`):
   - `ConnectionManager`: Central VPN connection management
   - `AuthService`: User authentication and credential storage
   - `BackendService`: Communication with Go backend (HTTP/WebSocket)
   - Platform-specific implementations for different OS features

3. **State Management**: Provider pattern with GetIt for dependency injection

4. **UI Layer**: Material Design with custom theming
   - Responsive layouts for desktop and mobile
   - SVG assets for scalable graphics
   - Internationalization (Chinese/English)

### Platform-Specific Implementations

**iOS/macOS** (`ui/flutter/ItForceCore/`):
- Swift implementation using NetworkExtension framework
- PacketTunnelProvider for VPN functionality
- Simplified architecture with VPNService as central manager

**Android** (`ui/flutter/android/`):
- Kotlin implementation using Android VpnService
- ConnectionManager handles VPN lifecycle
- In development (70% complete)

**Windows**:
- Uses Go backend via HTTP/WebSocket
- System tray integration
- Auto-start capability

## Key Technical Details

### SDWAN ZZVPN Protocol
- Custom binary protocol with TLV (Type-Length-Value) attributes
- Packet types: OPEN, OPEN_ACK, ECHO_REQUEST, ECHO_RESPONSE, DATA, CLOSE
- Supports AES-256-GCM and XOR encryption
- MTU handling with fragmentation support

### API Endpoints (Port 56544)
- `POST /api/login` - User authentication
- `GET /api/servers` - Server list
- `POST /api/connect` - Establish VPN
- `POST /api/disconnect` - Close VPN
- `GET /api/status` - Connection status
- `ws://localhost:56544/ws` - WebSocket events

### Configuration Files
- `configs/config.yaml` - Main configuration
- `configs/servers.json` - Server list (fallback)
- User credentials stored with simple encryption

### Important Constants
- Default API port: 56544
- Heartbeat interval: 30 seconds
- Network check interval: 10 seconds
- Reconnection attempts: 5
- MTU size: 1500 bytes

## Development Guidelines

1. **Error Handling**: Use the unified error system in `internal/common/errors/`
2. **Logging**: Use structured logging with appropriate log levels
3. **Platform Code**: Keep platform-specific code isolated in platform packages
4. **Protocol Compatibility**: Ensure all platforms maintain protocol compatibility
5. **Testing**: Add tests for new features, especially protocol-related code
6. **Performance**: Use object pools for frequently allocated objects
7. **Security**: Never log sensitive information (passwords, keys, tokens)

## Current Development Status

- **Windows/Linux**: Fully functional with Go backend
- **iOS/macOS**: Complete implementation with Swift
- **Android**: 70% complete, system integration in progress
- **Auto-update**: Implemented for all platforms
- **OEM Support**: Branding customization via configuration

## Common Issues and Solutions

1. **VPN Permission Issues**: 
   - iOS/macOS: Requires NetworkExtension entitlement
   - Android: Request VPN permission before connecting
   - Windows: Requires administrator privileges

2. **Build Issues**:
   - Flutter: Run `flutter clean` and `flutter pub get`
   - iOS: Delete `Pods/` and run `pod install`
   - Windows: Ensure Visual Studio 2022 installed

3. **Connection Issues**:
   - Check firewall settings
   - Verify server configuration
   - Review logs in `logs/` directory

## Notes for AI Assistants

- The project uses a commercial SDWAN protocol - focus on defensive security only
- Platform-specific code requires careful handling of permissions
- Always test protocol changes across all platforms
- Maintain backward compatibility with existing servers
- Consider performance impact of changes, especially on mobile
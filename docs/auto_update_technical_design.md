# 多平台自动更新功能技术设计文档

## 1. 概述

基于 `docs/upgrade_design.md` 文档，为多平台应用实现自动更新功能。支持 Windows/Android 下载安装包更新，iOS 跳转 App Store 更新。

### 1.1 核心目标

- **Flutter生态集成**: 优先使用现有第三方包，避免重复造轮子
- **用户体验优化**: 手动检查 + 自动检查 + 静默下载 + 智能提醒
- **多平台支持**: Windows/Android/iOS 差异化更新策略
- **安全可靠**: 文件完整性校验、HTTPS通信、错误处理

### 1.2 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter UI    │    │  Update Service │    │  Update Server  │
│                 │    │                 │    │                 │
│ - Settings UI   │◄──►│ - Version Check │◄──►│ - Version API   │
│ - Update Dialog │    │ - Download Mgr  │    │ - File Storage  │
│ - Progress UI   │    │ - Install Mgr   │    │ - Rules Engine  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 2. 依赖包选择

### 2.1 新增依赖

```yaml
dependencies:
  # 版本信息获取
  package_info_plus: ^8.0.0
  
  # 网络下载
  dio: ^5.4.0
  
  # 文件操作
  path_provider: ^2.1.1  # 已存在
  
  # URL启动 (App Store跳转)
  url_launcher: ^6.2.5   # 已存在
  
  # 加密哈希校验
  crypto: ^3.0.3         # 已存在
  
  # 定时器
  # 使用 Dart 内置 Timer
  
  # 本地存储
  shared_preferences: ^2.2.1  # 已存在
```

### 2.2 平台特定依赖

- **Android**: 使用 `android_intent_plus` 或系统 PackageInstaller
- **Windows**: 使用 `Process.start()` 启动安装程序
- **iOS**: 使用 `url_launcher` 跳转 App Store

## 3. 核心模块设计

### 3.1 数据模型

#### UpdateInfo 模型
```dart
class UpdateInfo {
  final bool updateAvailable;
  final String? version;
  final String? downloadUrl;
  final String? hash;
  final String? hashType;
  final Map<String, String>? releaseNotes;
  final bool forceUpdate;
  
  // 本地状态
  final UpdateStatus status;
  final String? localFilePath;
  final DateTime? lastCheckTime;
  final int skipCount;
}

enum UpdateStatus {
  none,           // 无更新
  available,      // 有更新可用
  downloading,    // 下载中
  downloaded,     // 下载完成
  installing,     // 安装中
  failed,         // 失败
  skipped,        // 用户跳过
}
```

#### UpdateConfig 模型
```dart
class UpdateConfig {
  final String serverUrl;
  final Duration checkInterval;
  final bool autoDownload;
  final bool wifiOnly;
  final int maxRetries;
  final Duration timeout;
  final int skipRemindHours;
}
```

### 3.2 服务层架构

#### UpdateService (核心服务)
```dart
class UpdateService {
  // 版本检查
  Future<UpdateInfo> checkForUpdate();
  
  // 下载管理
  Future<void> downloadUpdate(UpdateInfo updateInfo);
  
  // 安装管理
  Future<void> installUpdate(UpdateInfo updateInfo);
  
  // 定时检查
  void startPeriodicCheck();
  void stopPeriodicCheck();
  
  // 事件流
  Stream<UpdateInfo> get updateStream;
}
```

#### PlatformUpdateService (平台特定实现)
```dart
abstract class PlatformUpdateService {
  Future<void> installUpdate(String filePath);
  Future<void> openAppStore(String appId);
  String getDownloadDirectory();
  bool validateFile(String filePath, String hash, String hashType);
}
```

### 3.3 UI组件设计

#### 设置界面集成
- 在现有 `SettingsScreen` 添加"检查更新"按钮
- 显示当前版本号
- 手动触发更新检查

#### 更新对话框
- 显示版本信息和更新日志
- 强制更新 vs 可选更新
- 下载进度显示
- 多语言支持

## 4. 实现流程

### 4.1 版本检查流程

```mermaid
graph TD
    A[触发检查] --> B[获取当前版本]
    B --> C[构建请求参数]
    C --> D[调用服务端API]
    D --> E{有更新?}
    E -->|否| F[结束]
    E -->|是| G[解析更新信息]
    G --> H{平台类型}
    H -->|iOS| I[跳转App Store]
    H -->|Windows/Android| J[开始下载]
    J --> K[文件校验]
    K --> L[提示用户安装]
```

### 4.2 自动检查机制

- **VPN连接时**: 异步检查，不影响连接流程
- **定时检查**: 每小时检查一次，使用 `Timer.periodic`
- **应用启动**: 延迟5秒后检查，避免影响启动性能

### 4.3 下载管理

- **静默下载**: 后台下载，不阻塞用户操作
- **断点续传**: 使用 `dio` 的 Range 请求支持
- **网络策略**: 可配置仅WiFi下载
- **存储管理**: 自动清理旧版本文件

## 5. 平台特定实现

### 5.1 Windows 平台

```dart
class WindowsUpdateService extends PlatformUpdateService {
  @override
  Future<void> installUpdate(String filePath) async {
    // 启动安装程序
    await Process.start(filePath, ['/S'], runInShell: true);
  }
  
  @override
  String getDownloadDirectory() {
    // 使用临时目录
    return path.join(Directory.systemTemp.path, 'updates');
  }
}
```

### 5.2 Android 平台

```dart
class AndroidUpdateService extends PlatformUpdateService {
  @override
  Future<void> installUpdate(String filePath) async {
    // 使用系统安装器
    final intent = AndroidIntent(
      action: 'android.intent.action.VIEW',
      data: 'file://$filePath',
      type: 'application/vnd.android.package-archive',
    );
    await intent.launch();
  }
}
```

### 5.3 iOS 平台

```dart
class IOSUpdateService extends PlatformUpdateService {
  @override
  Future<void> openAppStore(String appId) async {
    final url = 'https://apps.apple.com/app/id$appId';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }
}
```

## 6. 错误处理与用户体验

### 6.1 错误处理策略

- **网络错误**: 自动重试，最大3次
- **下载失败**: 清理损坏文件，重新下载
- **校验失败**: 提示用户，记录日志
- **安装失败**: 提供手动安装指导

### 6.2 用户体验优化

- **跳过提醒**: 用户选择跳过后，1小时后再次提醒
- **强制更新**: 无法关闭的对话框，只能更新
- **进度显示**: 实时显示下载进度
- **后台下载**: 不影响正常使用

## 7. 配置与本地化

### 7.1 配置管理

```dart
class UpdateConfig {
  static const String serverUrl = 'https://api.example.com';
  static const Duration checkInterval = Duration(hours: 1);
  static const bool autoDownload = true;
  static const bool wifiOnly = true;
  static const int maxRetries = 3;
  static const Duration timeout = Duration(minutes: 5);
  static const int skipRemindHours = 1;
}
```

### 7.2 多语言支持

在 `app_localizations.dart` 添加更新相关文本：

```dart
// 中文
'updateAvailable': '发现新版本',
'updateNow': '立即更新',
'updateLater': '稍后提醒',
'downloading': '下载中...',
'installNow': '立即安装',

// 英文
'updateAvailable': 'Update Available',
'updateNow': 'Update Now',
'updateLater': 'Remind Later',
'downloading': 'Downloading...',
'installNow': 'Install Now',
```

## 8. 测试策略

### 8.1 单元测试

- 版本比较逻辑测试
- 文件校验功能测试
- 配置解析测试
- 错误处理测试

### 8.2 集成测试

- 完整更新流程测试
- 平台特定功能测试
- 网络异常处理测试
- UI交互测试

### 8.3 手动测试

- 不同平台安装测试
- 强制更新场景测试
- 网络环境变化测试
- 用户交互体验测试

## 9. 部署与监控

### 9.1 服务端配置

确保服务端 API 按照 `docs/upgrade_design.md` 规范实现：

- POST `/update/check` 接口
- 支持平台类型参数
- 返回标准化响应格式
- 配置文件热更新

### 9.2 客户端监控

- 更新检查成功率
- 下载成功率
- 安装成功率
- 用户行为统计

## 10. 安全考虑

### 10.1 文件完整性

- 强制使用 SHA-256 校验
- 下载完成后立即校验
- 校验失败自动删除文件

### 10.2 网络安全

- 强制 HTTPS 通信
- 证书验证
- 请求签名（可选）

### 10.3 权限管理

- Android 安装权限申请
- Windows UAC 提升
- iOS App Store 跳转

---

**下一步**: 开始实现核心模块，按照以下顺序：
1. 数据模型定义
2. 平台服务工厂扩展
3. 更新服务核心逻辑
4. UI组件集成
5. 平台特定实现
6. 测试与优化

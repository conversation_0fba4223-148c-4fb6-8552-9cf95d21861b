1. 概述 (Overview)
本文档旨在设计一个稳定、灵活且支持灰度发布的客户端软件自动更新方案。该方案适用于 Windows 和 Android 平台，通过客户端与升级服务器的协同工作，实现更新的智能推送、安全下载和可靠安装。

核心流程：
1. 客户端定时轮询：客户端在启动或后台定时向更新服务器发起更新检查请求。
2. 服务器策略决策：服务器根据客户端上报的身份信息（版本、平台、域、OEM等），依据预设的规则策略，判断是否需要升级。
3. 响应更新指令：若需升级，服务器返回新版本的详细信息，包括下载地址、版本号、文件哈希值（用于校验）和更新日志。
4. 客户端静默下载：客户端在后台下载更新包。
5. 文件完整性校验：下载完成后，客户端使用服务器下发的哈希值校验文件完整性与正确性。
6. 用户确认安装：校验通过后，客户端弹出对话框，提示用户有新版本可用，并由用户确认是否立即安装

2. 系统架构
+----------------+      (1) Check for Update      +-----------------+      (3) Load Rules      +--------------------+
|                |  (HTTPS POST Request with      |                 |------------------------->|                    |
| Client App     |   client info & version)       |  Go Update      |<-------------------------| update_rules.yaml  |
| (Win/Android)  |------------------------------->|  Service        |                          | (Config File)      |
|                |                                |  (Server)       |                          |                    |
|                |<-------------------------------|                 |                          +--------------------+
+----------------+   (2) Update Response (JSON)   +-----------------+
                         (URL, Hash, Version...)
- 配置高度可读与结构化：使用 YAML 格式作为配置文件。YAML 相比简单的键值对或 ini 文件，对人类更友好，能更好地表达复杂的嵌套逻辑和列表，非常适合定义规则。
- 增强安全性：
  - 强制使用 HTTPS：所有客户端与服务器的通信都必须在 HTTPS 加密信道中进行，防止中间人攻击和数据篡改。
  - 增强哈希算法：除了 MD5，强烈建议并优先使用 SHA-256。MD5 已被证明存在碰撞风险，不适用于安全校验。
- 精细化版本比较：不仅仅是判断版本号是否相等，服务器端的逻辑应支持更丰富的比较，如 > (大于), < (小于), >= (大于等于), <= (小于等于), != (不等于)，以支持修复性更新或对特定旧版本的更新。
- 明确的 API 契约：定义版本化的 RESTful API 接口，使前后端开发解耦，便于未来扩展。
- 增加强制更新与更新日志：在服务器响应中增加 forceUpdate (强制更新) 标记和 releaseNotes (更新日志) 字段，以应对重大安全漏洞修复和提升用户体验。

3. 服务端设计 (Go-based Service)
3.1. API 接口设计 (API Endpoint Design)

- Endpoint: POST /update/check
- Method: POST (因为请求体中包含较多信息，使用POST更合适)
- Content-Type: application/json

Request Body (请求体):
{
  "type": "windows",  // 客户端类型: "windows" or "android"
  "version": "1.2.3",   // 客户端当前版本号
  "domain": "internal-domain-A", // 客户域信息
  "oemName": "default" // OEM 版本名称
}

Response Body (响应体):
- 情况一：需要更新
{
  "updateAvailable": true,
  "version": "1.3.0",
  "downloadUrl": "https://your-cdn.com/updates/app-1.3.0.exe",
  "hash": "a1b2c3d4e5f6...", // 文件的哈希值
  "hashType": "SHA-256",   // 哈希算法类型: "SHA-256" or "MD5"
  "releaseNotes": {     
    "en": "1. Fixed a bug.\n2. Added a new feature.",     
    "zh": "1. 修复了XX bug。\n2. 新增了YY功能。" 
  }
  "forceUpdate": false // 是否强制更新 (true/false)
}
- 情况二：无需更新
{
  "updateAvailable": false
}

3.2. 配置文件设计 (update_rules.yaml)

配置决定了所有升级策略。Go 服务在启动时加载此文件，并根据其内容进行决策。
设计思路:
- 使用一个名为 updateRules 的规则列表。
- 服务器按顺序（或按优先级）遍历列表中的每个规则。
- 第一个完全匹配客户端上报信息条件的规则将被采用，并停止后续匹配。
- 每个规则包含 conditions (匹配条件) 和 targetUpdate (满足条件后返回的更新目标)。
- conditions 内部的所有条件必须同时满足（AND 逻辑）。
-  conditions 中某field为空时为通配。 

update_rules.yaml 示例:
# ==========================================================
# Part 1: 更新包定义库 (Update Package Definitions)
# ==========================================================
updatePackages:
  # Windows 更新包定义
  win_v1.5.1_security_patch:
    version: "1.5.1"
    downloadUrl: "https://your-cdn.com/updates/win-app-1.5.1.exe"
    hash: "sha256_hash_for_1.5.1_exe"
    hashType: "SHA-256"
    releaseNotes:
      en: "Important Security Update:\n- Patched remote execution vulnerability CVE-2025-XXXX."
      zh: "重要安全更新：\n- 修复了远程执行漏洞 CVE-2025-XXXX。"
    forceUpdate: true

  win_v1.4.5_oem_b:
    version: "1.4.5-oem-b"
    downloadUrl: "https://your-cdn.com/updates/win-app-1.4.5-oem-b.exe"
    hash: "sha256_hash_for_oem_b"
    hashType: "SHA-256"
    releaseNotes:
      en: "Customized version update:\n- Updated brand logo and theme color."
      zh: "定制版更新：\n- 更新了品牌Logo和主题颜色。"
    forceUpdate: false

  # Android 更新包定义
  android_v2.0.0_beta:
    version: "2.0.0-beta"
    downloadUrl: "https://your-cdn.com/updates/android-app-2.0.0-beta.apk"
    hash: "sha256_hash_for_beta_apk"
    hashType: "SHA-256"
    releaseNotes:
      en: "Internal Beta:\n- All new user interface.\n- Performance enhancements."
      zh: "内部测试版：\n- 全新UI界面。\n- 性能优化。"
    forceUpdate: false


# ==========================================================
# Part 2: 更新规则 (Update Rules)
# ==========================================================
updateRules:
  # 规则1: 对内部域的特定旧版本Android客户端进行灰度测试
  - description: "灰度发布：Android 内部域 2.0.0 版本"
    enabled: true
    priority: 100
    conditions:
      - { field: "type", operator: "eq", value: "android" }
      - { field: "version", operator: "lt", value: "2.0.0" }
      - { field: "domain", operator: "in", value: ["domain-a.com", "domain-b.com", "domain-c.org"] }
    # 引用上面定义的更新包
    targetPackage: "android_v2.0.0_beta"

  # 规则2: 强制更新所有1.5.0以下的有严重漏洞的Windows版本
  - description: "安全更新：强制修复所有低于1.5.0的Windows客户端"
    enabled: true
    priority: 90
    conditions:
      - { field: "type", operator: "eq", value: "windows" }
      - { field: "version", operator: "lt", value: "1.5.0" }
    # 引用上面定义的更新包
    targetPackage: "win_v1.5.1_security_patch"

  # 规则3: 对某个特定的OEM厂商推送定制版更新
  - description: "OEM 定制版：为 'oem-vendor-b' 推送定制皮肤的Windows版本"
    enabled: true
    priority: 80
    conditions:
      - { field: "type", operator: "eq", value: "windows" }
      - { field: "oemName", operator: "eq", value: "oem-vendor-b" }
      - { field: "version", operator: "lt", value: "1.4.5" }
    # 引用上面定义的更新包
    targetPackage: "win_v1.4.5_oem_b"
支持的 operator (操作符) 定义:
- eq: 等于 (string)
- neq: 不等于 (string)
- gt: 大于 (version)
- lt: 小于 (version)
- gte: 大于等于 (version)
- lte: 小于等于 (version)
- in: 包含于 (value 是一个 string 列表, e.g., ["domain-a", "domain-b"])

3.3. 服务端处理逻辑
1. 启动：Go 服务启动时，执行以下操作：
  - 读取并解析 update_rules.yaml 文件。
  - 将 updatePackages 部分加载到一个 map[string]PackageInfo 结构中，key 是包的唯一标识符（如 win_v1.5.1_security_patch），value 是包的详细信息结构体。
  - 将 updateRules 加载到规则列表（[]Rule）中，并按 priority 排序。
2. 规则匹配：处理逻辑与第一版相同，即遍历规则列表，找到第一个满足所有 conditions 的规则。
3. 构建响应 (核心变化)：
  - 当一个规则匹配成功后，服务器从该规则中获取 targetPackage 的值（例如，"win_v1.5.1_security_patch"）。
  - 使用这个值作为 key，在内存中的 updatePackages map 里查找对应的包信息。
  - 如果找到，将这个包的完整信息（版本、URL、哈希、多语言日志等）构建成 JSON 响应体返回给客户端。
  - 如果根据 key 没有找到对应的包（配置错误），则应记录错误日志，并向客户端返回 {"updateAvailable": false}。
4. 配置更新：提供API reload配置文件更新。curl -X POST "xxx/admin/reload"

4. 客户端设计
4.1. 工作流程
1. 触发检查：
  - 应用启动时立即触发一次检查。
  - 应用在后台时，启动一个定时器，例如每4小时检查一次。可以增加随机延迟（如0-15分钟），避免所有客户端在同一时刻请求服务器（惊群效应）。
2. 发送请求：收集 clientType, clientVersion, customerDomain, oemName 等信息，向服务器 POST HTTPS 请求。
3. 处理响应：
  - 如果 updateAvailable 为 false，则流程结束，等待下一次触发。
  - 如果 updateAvailable 为 true： 
    - a. 下载时应优先复用本地已缓存包，校验通过即可直接弹出安装提示。
    - b. 开始下载：在后台启动下载任务，从 downloadUrl 下载文件。下载过程应不影响用户正常使用。可以考虑在 Wi-Fi 环境下才自动下载。
    -  c. 校验文件：下载完成后，计算本地文件的哈希值（使用 hashType 指定的算法），与服务器返回的 hash 值进行比对。 
    - d. 校验失败：如果哈希不匹配，删除已下载的损坏文件，并记录一条本地错误日志。可选择重试1-2次。多次失败后放弃本次更新。 
    - e. 校验成功： 
      - i. 强制更新 (forceUpdate: true): 显示一个无法关闭的对话框，展示 releaseNotes，并只有一个“立即升级”按钮。用户点击后，启动安装程序。 
      - ii. 普通更新 (forceUpdate: false): 显示一个标准对话框，展示 releaseNotes，并提供“立即升级”和“稍后提醒”按钮。若用户选择稍后，则在下一次启动或特定时间后再次提醒。
4. 执行安装：
  - Windows: 启动下载的 .exe  安装包。
  - Android: 触发系统的 PackageInstaller 来安装 .apk 文件（需要申请相应权限）。


// 调试关于界面版本号的脚本
import 'dart:io';

void main() {
  print('调试关于界面版本号...');
  
  // 检查constants.dart
  print('\n1. 检查constants.dart:');
  final constantsFile = File('ui/flutter/lib/utils/constants.dart');
  if (constantsFile.existsSync()) {
    final content = constantsFile.readAsStringSync();
    final regex = RegExp(r"const String kAppVersion = '([^']+)'");
    final match = regex.firstMatch(content);
    if (match != null) {
      print('   kAppVersion = ${match.group(1)}');
    }
  }
  
  // 检查about_screen.dart
  print('\n2. 检查about_screen.dart:');
  final aboutFile = File('ui/flutter/lib/screens/about_screen.dart');
  if (aboutFile.existsSync()) {
    final content = aboutFile.readAsStringSync();
    final lines = content.split('\n');
    
    // 查找version变量定义
    for (int i = 0; i < lines.length; i++) {
      if (lines[i].contains('final String version') || lines[i].contains('String version')) {
        print('   第${i + 1}行: ${lines[i].trim()}');
      }flutter clean

    }
    
    // 查找导入constants.dart的行
    for (int i = 0; i < lines.length; i++) {
      if (lines[i].contains('constants.dart')) {
        print('   第${i + 1}行: ${lines[i].trim()}');
      }
    }
  }
  
  // 检查pubspec.yaml
  print('\n3. 检查pubspec.yaml:');
  final pubspecFile = File('ui/flutter/pubspec.yaml');
  if (pubspecFile.existsSync()) {
    final content = pubspecFile.readAsStringSync();
    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      if (lines[i].contains('version:')) {
        print('   第${i + 1}行: ${lines[i].trim()}');
      }
    }
  }
  
  print('\n分析完成。');
  print('如果constants.dart中kAppVersion是1.2.0，但界面显示1.1.0，');
  print('可能的原因：');
  print('1. Flutter应用需要重新编译（flutter clean && flutter build）');
  print('2. 需要热重启而不是热重载');
  print('3. 可能存在编译缓存问题');
}

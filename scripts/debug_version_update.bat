@echo off
REM ========================================================================
REM 调试版本更新脚本
REM ========================================================================

setlocal enabledelayedexpansion

echo [DEBUG] 调试版本更新功能...

REM 设置项目根目录
set "PROJECT_ROOT=%~dp0.."
set "VERSION_FILE=%PROJECT_ROOT%\VERSION"

REM 读取当前版本
if exist "%VERSION_FILE%" (
    set /p CURRENT_VERSION=<"%VERSION_FILE%"
    echo [DEBUG] 当前版本: !CURRENT_VERSION!
) else (
    echo [ERROR] VERSION文件不存在
    exit /b 1
)

echo.
echo [DEBUG] 测试Go后端版本更新的PowerShell命令...
set "main_file=%PROJECT_ROOT%\cmd\vpn-service\main.go"
if exist "%main_file%" (
    echo [DEBUG] 检查当前Go版本号...
    findstr /C:"version    =" "%main_file%"
    
    echo [DEBUG] 测试正则表达式匹配...
    powershell -Command "Get-Content '%main_file%' | Select-String 'version\s+=\s+\"[^\"]*\"'"
    
    echo [DEBUG] 执行更新命令...
    powershell -Command "$content = Get-Content '%main_file%' -Encoding UTF8; Write-Host 'Original content found:'; $content | Select-String 'version\s+=\s+'; $content = $content -replace 'version\s+=\s+\"[^\"]*\"', 'version    = \"!CURRENT_VERSION!\"'; Write-Host 'After replacement:'; $content | Select-String 'version\s+=\s+'; $content | Set-Content '%main_file%' -Encoding UTF8"
    
    echo [DEBUG] 检查更新后的Go版本号...
    findstr /C:"version    =" "%main_file%"
) else (
    echo [ERROR] Go主文件不存在: %main_file%
)

echo.
echo [DEBUG] 测试完成

endlocal

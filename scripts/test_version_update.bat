@echo off
REM ========================================================================
REM 测试版本更新脚本
REM ========================================================================

setlocal enabledelayedexpansion

echo [INFO] 测试版本更新功能...

REM 设置项目根目录
set "PROJECT_ROOT=%~dp0.."
set "VERSION_FILE=%PROJECT_ROOT%\VERSION"

REM 读取当前版本
if exist "%VERSION_FILE%" (
    set /p CURRENT_VERSION=<"%VERSION_FILE%"
    echo [INFO] 当前版本: !CURRENT_VERSION!
) else (
    echo [ERROR] VERSION文件不存在
    exit /b 1
)

echo.
echo [INFO] 测试Go后端版本更新...
set "main_file=%PROJECT_ROOT%\cmd\vpn-service\main.go"
if exist "%main_file%" (
    echo [INFO] 检查当前Go版本号...
    findstr /C:"version    =" "%main_file%"
    
    echo [INFO] 执行PowerShell更新命令...
    powershell -Command "$content = Get-Content '%main_file%' -Encoding UTF8; $content = $content -replace 'version\s+=\s+\"[^\"]*\"', 'version    = \"!CURRENT_VERSION!\"'; $content | Set-Content '%main_file%' -Encoding UTF8"
    
    echo [INFO] 检查更新后的Go版本号...
    findstr /C:"version    =" "%main_file%"
) else (
    echo [ERROR] Go主文件不存在: %main_file%
)

echo.
echo [INFO] 测试Flutter版本更新...
set "pubspec_file=%PROJECT_ROOT%\ui\flutter\pubspec.yaml"
if exist "%pubspec_file%" (
    echo [INFO] 检查当前Flutter版本号...
    findstr /C:"version:" "%pubspec_file%"
    findstr /C:"msix_version:" "%pubspec_file%"
    
    echo [INFO] 执行PowerShell更新命令...
    powershell -Command "$content = Get-Content '%pubspec_file%' -Encoding UTF8; $content = $content -replace '^version:\s*[^\s]*', 'version: !CURRENT_VERSION!+1'; $content = $content -replace 'msix_version:\s*[^\s]*', 'msix_version: !CURRENT_VERSION!.0'; $content | Set-Content '%pubspec_file%' -Encoding UTF8"
    
    echo [INFO] 检查更新后的Flutter版本号...
    findstr /C:"version:" "%pubspec_file%"
    findstr /C:"msix_version:" "%pubspec_file%"
) else (
    echo [ERROR] pubspec.yaml文件不存在: %pubspec_file%
)

echo.
echo [INFO] 测试constants.dart版本更新...
set "constants_file=%PROJECT_ROOT%\ui\flutter\lib\utils\constants.dart"
if exist "%constants_file%" (
    echo [INFO] 检查当前constants.dart版本号...
    findstr /C:"const String kAppVersion" "%constants_file%"
    
    echo [INFO] 执行PowerShell更新命令...
    powershell -Command "$content = Get-Content '%constants_file%' -Encoding UTF8; $content = $content -replace 'const String kAppVersion = ''[^'']*''', 'const String kAppVersion = ''!CURRENT_VERSION!'''; $content | Set-Content '%constants_file%' -Encoding UTF8"
    
    echo [INFO] 检查更新后的constants.dart版本号...
    findstr /C:"const String kAppVersion" "%constants_file%"
) else (
    echo [ERROR] constants.dart文件不存在: %constants_file%
)

echo.
echo [INFO] 测试完成

endlocal

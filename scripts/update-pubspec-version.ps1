# PowerShell脚本用于更新Flutter pubspec.yaml中的版本号
# 参数：
#   -Version: 新版本号 (例如: "1.2.0")
#   -PubspecPath: pubspec.yaml文件路径

param(
    [Parameter(Mandatory=$true)]
    [string]$Version,

    [Parameter(Mandatory=$true)]
    [string]$PubspecPath
)

# 检查文件是否存在
if (-not (Test-Path $PubspecPath)) {
    Write-Error "pubspec.yaml文件不存在: $PubspecPath"
    exit 1
}

try {
    # 读取文件内容
    $content = Get-Content $PubspecPath -Encoding UTF8
    
    # 更新version字段 (格式: version: 1.1.0+1)
    $content = $content -replace '^version:\s*[^\s]*', "version: $Version+1"
    
    # 更新msix_version字段 (格式: msix_version: *******)
    $content = $content -replace 'msix_version:\s*[^\s]*', "msix_version: $Version.0"
    
    # 写回文件
    $content | Set-Content $PubspecPath -Encoding UTF8
    
    Write-Host "[SUCCESS] pubspec.yaml版本已更新为: $Version" -ForegroundColor Green
    exit 0
}
catch {
    Write-Error "更新pubspec.yaml失败: $($_.Exception.Message)"
    exit 1
}

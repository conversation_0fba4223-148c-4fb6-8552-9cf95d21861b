@echo off
REM ========================================================================
REM 版本同步验证脚本 - Windows
REM ========================================================================
REM 用于验证所有组件的版本号是否与VERSION文件同步

setlocal enabledelayedexpansion

echo [INFO] 验证版本号同步状态...

REM 读取VERSION文件
set VERSION_FILE=%~dp0..\VERSION
if exist "%VERSION_FILE%" (
    set /p EXPECTED_VERSION=<"%VERSION_FILE%"
    echo [INFO] 期望版本号: !EXPECTED_VERSION!
) else (
    echo [ERROR] VERSION文件不存在
    exit /b 1
)

set ERROR_COUNT=0

echo.
echo ========================================
echo 检查各组件版本号
echo ========================================

REM 1. 检查Go后端main.go中的版本号
set GO_MAIN_FILE=%~dp0..\cmd\vpn-service\main.go
if exist "%GO_MAIN_FILE%" (
    echo [INFO] 检查Go后端版本号...
    findstr /C:"version    = \"!EXPECTED_VERSION!\"" "%GO_MAIN_FILE%" >nul
    if !ERRORLEVEL! equ 0 (
        echo [✓] Go后端版本号正确: !EXPECTED_VERSION!
    ) else (
        echo [✗] Go后端版本号不匹配
        findstr /C:"version    =" "%GO_MAIN_FILE%"
        set /a ERROR_COUNT+=1
    )
) else (
    echo [WARNING] Go主文件不存在: %GO_MAIN_FILE%
)

REM 2. 检查Flutter pubspec.yaml中的版本号
set PUBSPEC_FILE=%~dp0..\ui\flutter\pubspec.yaml
if exist "%PUBSPEC_FILE%" (
    echo [INFO] 检查Flutter pubspec.yaml版本号...
    findstr /C:"version: !EXPECTED_VERSION!+1" "%PUBSPEC_FILE%" >nul
    if !ERRORLEVEL! equ 0 (
        echo [✓] Flutter pubspec.yaml版本号正确: !EXPECTED_VERSION!+1
    ) else (
        echo [✗] Flutter pubspec.yaml版本号不匹配
        findstr /C:"version:" "%PUBSPEC_FILE%"
        set /a ERROR_COUNT+=1
    )
    
    findstr /C:"msix_version: !EXPECTED_VERSION!.0" "%PUBSPEC_FILE%" >nul
    if !ERRORLEVEL! equ 0 (
        echo [✓] Flutter msix_version版本号正确: !EXPECTED_VERSION!.0
    ) else (
        echo [✗] Flutter msix_version版本号不匹配
        findstr /C:"msix_version:" "%PUBSPEC_FILE%"
        set /a ERROR_COUNT+=1
    )
) else (
    echo [WARNING] pubspec.yaml文件不存在: %PUBSPEC_FILE%
)

REM 3. 检查Flutter constants.dart中的版本号
set CONSTANTS_FILE=%~dp0..\ui\flutter\lib\utils\constants.dart
if exist "%CONSTANTS_FILE%" (
    echo [INFO] 检查Flutter constants.dart版本号...
    findstr /C:"const String kAppVersion = '!EXPECTED_VERSION!'" "%CONSTANTS_FILE%" >nul
    if !ERRORLEVEL! equ 0 (
        echo [✓] Flutter constants.dart版本号正确: !EXPECTED_VERSION!
    ) else (
        echo [✗] Flutter constants.dart版本号不匹配
        findstr /C:"const String kAppVersion" "%CONSTANTS_FILE%"
        set /a ERROR_COUNT+=1
    )
) else (
    echo [WARNING] constants.dart文件不存在: %CONSTANTS_FILE%
)

REM 4. 检查安装程序setup.iss中的版本号
set SETUP_FILE=%~dp0..\installer\setup.iss
if exist "%SETUP_FILE%" (
    echo [INFO] 检查安装程序setup.iss版本号...
    findstr /C:"#define MyAppVersion \"!EXPECTED_VERSION!\"" "%SETUP_FILE%" >nul
    if !ERRORLEVEL! equ 0 (
        echo [✓] 安装程序setup.iss版本号正确: !EXPECTED_VERSION!
    ) else (
        echo [✗] 安装程序setup.iss版本号不匹配
        findstr /C:"#define MyAppVersion" "%SETUP_FILE%"
        set /a ERROR_COUNT+=1
    )
) else (
    echo [WARNING] setup.iss文件不存在: %SETUP_FILE%
)

echo.
echo ========================================
echo 验证结果
echo ========================================

if !ERROR_COUNT! equ 0 (
    echo [SUCCESS] 所有组件版本号已同步: !EXPECTED_VERSION!
    exit /b 0
) else (
    echo [ERROR] 发现 !ERROR_COUNT! 个版本号不匹配的组件
    echo [INFO] 请运行以下命令同步版本号:
    echo [INFO]   scripts\version_manager.bat update-all
    exit /b 1
)

endlocal

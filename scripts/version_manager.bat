@echo off
REM ========================================================================
REM 版本管理脚本 - Windows
REM ========================================================================
REM 用于读取、更新和管理项目版本号的统一工具

setlocal enabledelayedexpansion

REM 脚本配置
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."
set "VERSION_FILE=%PROJECT_ROOT%\VERSION"
set "CONFIG_FILE=%PROJECT_ROOT%\version.config.yaml"

REM 默认参数
set "DRY_RUN=false"
set "VERBOSE=false"
set "NO_GIT=false"

REM 日志函数
:log_info
echo [INFO] %~1
goto :eof

:log_success
echo [SUCCESS] %~1
goto :eof

:log_warning
echo [WARNING] %~1
goto :eof

:log_error
echo [ERROR] %~1
goto :eof

REM 显示帮助信息
:show_help
echo Version Manager - ItForce WAN
echo.
echo Usage: %~nx0 ^<command^>
echo.
echo Commands:
echo   update-all    Update all component versions
echo   help          Show this help
echo.
goto :eof

REM 检查版本文件是否存在
:check_version_file
if not exist "%VERSION_FILE%" (
    call :log_error "版本文件不存在: %VERSION_FILE%"
    call :log_info "请先创建版本文件或运行初始化命令"
    exit /b 1
)
goto :eof

REM 读取当前版本
:get_version
call :check_version_file
if errorlevel 1 exit /b 1
set /p CURRENT_VERSION=<"%VERSION_FILE%"
echo %CURRENT_VERSION%
goto :eof

REM 验证版本格式
:validate_version
set "version=%~1"
echo %version% | findstr /r "^[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*$" >nul
if errorlevel 1 (
    call :log_error "无效的版本格式: %version%"
    call :log_info "版本格式应为: MAJOR.MINOR.PATCH (例如: 1.0.0)"
    exit /b 1
)
exit /b 0

REM 设置版本
:set_version
set "new_version=%~1"

if "%new_version%"=="" (
    call :log_error "请提供版本号"
    exit /b 1
)

call :validate_version "%new_version%"
if errorlevel 1 exit /b 1

call :get_version
set "current_version=%CURRENT_VERSION%"

if "%DRY_RUN%"=="true" (
    call :log_info "DRY RUN: 将版本从 %current_version% 更新为 %new_version%"
    goto :eof
)

echo %new_version%>"%VERSION_FILE%"
call :log_success "版本已更新: %current_version% -> %new_version%"
goto :eof

REM 递增版本号
:bump_version
set "bump_type=%~1"
call :get_version
set "current_version=%CURRENT_VERSION%"

REM 解析当前版本
for /f "tokens=1,2,3 delims=." %%a in ("%current_version%") do (
    set "major=%%a"
    set "minor=%%b"
    set "patch=%%c"
)

if "%bump_type%"=="major" (
    set /a major+=1
    set "minor=0"
    set "patch=0"
) else if "%bump_type%"=="minor" (
    set /a minor+=1
    set "patch=0"
) else if "%bump_type%"=="patch" (
    set /a patch+=1
) else (
    call :log_error "无效的递增类型: %bump_type%"
    call :log_info "支持的类型: major, minor, patch"
    exit /b 1
)

set "new_version=%major%.%minor%.%patch%"
call :set_version "%new_version%"
goto :eof

REM 生成构建版本号
:generate_build_version
call :get_version
set "version=%CURRENT_VERSION%"

REM 获取时间戳
for /f "tokens=1-6 delims=/:. " %%a in ("%date% %time%") do (
    set "timestamp=%%c%%a%%b%%d%%e%%f"
)
set "timestamp=%timestamp: =0%"
set "timestamp=%timestamp:~0,14%"

REM 获取 Git 提交信息
for /f "tokens=*" %%i in ('git rev-parse --short HEAD 2^>nul') do set "git_commit=%%i"
if "%git_commit%"=="" set "git_commit=unknown"

set "build_version=%version%-%git_commit%-%timestamp%"
echo %build_version%
goto :eof

REM 更新 Go 后端版本
:update_go_version
set "version=%~1"
set "main_file=%PROJECT_ROOT%\cmd\vpn-service\main.go"

if not exist "%main_file%" (
    call :log_warning "Go 主文件不存在: %main_file%"
    exit /b 1
)

if "%DRY_RUN%"=="true" (
    call :log_info "DRY RUN: 将更新 Go 版本变量为 %version%"
    goto :eof
)

REM 使用 PowerShell 更新版本变量
powershell -Command "$content = Get-Content '%main_file%' -Encoding UTF8; $content = $content -replace 'version\s+=\s+\"[^\"]*\"', 'version    = \"%version%\"'; $content | Set-Content '%main_file%' -Encoding UTF8"

call :log_success "已更新 Go 后端版本: %version%"
goto :eof

REM 更新 Flutter 版本
:update_flutter_version
set "version=%~1"
set "pubspec_file=%PROJECT_ROOT%\ui\flutter\pubspec.yaml"
set "constants_file=%PROJECT_ROOT%\ui\flutter\lib\utils\constants.dart"

if not exist "%pubspec_file%" (
    call :log_warning "Flutter pubspec.yaml 不存在: %pubspec_file%"
    exit /b 1
)

if "%DRY_RUN%"=="true" (
    call :log_info "DRY RUN: 将更新 Flutter 版本为 %version%"
    goto :eof
)

REM 使用 PowerShell 更新 pubspec.yaml 版本
powershell -Command "$content = Get-Content '%pubspec_file%' -Encoding UTF8; $content = $content -replace '^version:\s*[^\s]*', 'version: %version%+1'; $content = $content -replace 'msix_version:\s*[^\s]*', 'msix_version: %version%.0'; $content | Set-Content '%pubspec_file%' -Encoding UTF8"

REM 更新 constants.dart 中的版本号
if exist "%constants_file%" (
    powershell -Command "$content = Get-Content '%constants_file%' -Encoding UTF8; $content = $content -replace 'const String kAppVersion = ''[^'']*''', 'const String kAppVersion = ''%version%'''; $content | Set-Content '%constants_file%' -Encoding UTF8"
    call :log_success "已更新 constants.dart 版本: %version%"
)

call :log_success "已更新 Flutter 版本: %version%"
goto :eof

REM 更新安装程序版本
:update_installer_version
set "version=%~1"
set "setup_file=%PROJECT_ROOT%\installer\setup.iss"

if not exist "%setup_file%" (
    call :log_warning "安装程序脚本不存在: %setup_file%"
    exit /b 1
)

if "%DRY_RUN%"=="true" (
    call :log_info "DRY RUN: 将更新安装程序版本为 %version%"
    goto :eof
)

REM 使用 PowerShell 更新版本定义
powershell -Command "$content = Get-Content '%setup_file%' -Encoding UTF8; $content = $content -replace '#define MyAppVersion \"[^\"]*\"', '#define MyAppVersion \"%version%\"'; $content | Set-Content '%setup_file%' -Encoding UTF8"

call :log_success "已更新安装程序版本: %version%"
goto :eof

REM 更新所有组件版本
:update_all_components
call :get_version
set "version=%CURRENT_VERSION%"

call :log_info "开始更新所有组件版本为: %version%"

call :update_go_version "%version%"
call :update_flutter_version "%version%"
call :update_installer_version "%version%"

if "%NO_GIT%" neq "true" if "%DRY_RUN%" neq "true" (
    REM 创建 Git 标签
    git rev-parse --git-dir >nul 2>&1
    if not errorlevel 1 (
        set "tag=v%version%"
        git tag -l | findstr /x "!tag!" >nul
        if errorlevel 1 (
            git tag "!tag!"
            call :log_success "已创建 Git 标签: !tag!"
        ) else (
            call :log_warning "Git 标签已存在: !tag!"
        )
    )
)

call :log_success "所有组件版本更新完成"
goto :eof

REM 解析命令行参数
:parse_args
if "%~1"=="--dry-run" (
    set "DRY_RUN=true"
    shift
    goto :parse_args
)
if "%~1"=="--verbose" (
    set "VERBOSE=true"
    shift
    goto :parse_args
)
if "%~1"=="--no-git" (
    set "NO_GIT=true"
    shift
    goto :parse_args
)
goto :eof

REM 主程序入口
call :parse_args %*

REM 移除已解析的参数
:remove_parsed_args
if "%~1"=="--dry-run" (
    shift
    goto :remove_parsed_args
)
if "%~1"=="--verbose" (
    shift
    goto :remove_parsed_args
)
if "%~1"=="--no-git" (
    shift
    goto :remove_parsed_args
)

REM 主命令处理
set "command=%~1"
if "%command%"=="" set "command=help"

if "%command%"=="update-all" (
    call :update_all_components
) else (
    call :show_help
)

/**
 * FILE: PlatformChannelHandler.kt
 *
 * DESCRIPTION:
 *     Platform Channel handler for Flutter-Kotlin communication.
 *     Provides VPN management interface equivalent to iOS PlatformChannelHandler.
 *     Bridges Flutter UI with Android VPN services through Method Channel and Event Channel.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create Android Platform Channel handler
 */

package com.panabit.client.presentation.flutter

import android.content.Context
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.panabit.client.domain.interfaces.VPNServiceInterface
import com.panabit.client.connection.models.VPNState
import com.panabit.client.connection.models.ServerInfo
import com.panabit.client.platform.PermissionManager
import com.panabit.client.infrastructure.error.VPNServiceError
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.RoutingConfigurationManager
import org.json.JSONArray

/**
 * NAME: PlatformChannelHandler
 *
 * DESCRIPTION:
 *     Platform Channel handler that bridges Flutter UI with Android VPN services.
 *     Provides method channel for VPN operations and event channel for real-time updates.
 *     Equivalent to iOS PlatformChannelHandler functionality.
 *
 *     DESIGN: Follows Infrastructure→Domain→Presentation layered architecture.
 *     Depends on VPNServiceInterface abstraction, adhering to DIP principle.
 *
 * PROPERTIES:
 *     vpnServiceInterface - VPN service abstraction interface
 *     context - Android context for system service access
 *     methodChannel - Flutter method channel for VPN operations
 *     eventChannel - Flutter event channel for real-time updates
 *     eventSink - Flutter event sink for pushing updates
 */
class PlatformChannelHandler(
    private val vpnServiceInterface: VPNServiceInterface,
    private val context: Context,
    private val routingConfigurationManager: RoutingConfigurationManager
) : EventChannel.StreamHandler {

    companion object {
        // Channel names matching iOS platform for consistency
        const val METHOD_CHANNEL_NAME = "panabit_client/methods"
        const val EVENT_CHANNEL_NAME = "panabit_client/events"

        // Permission request codes
        const val VPN_PERMISSION_REQUEST_CODE = 1001

        // Pre-allocated response templates to reduce object creation
        private val EMPTY_SUCCESS_RESPONSE = mapOf("success" to true, "data" to null)
        private val EMPTY_FAILURE_RESPONSE = mapOf("success" to false)
    }

    private var methodChannel: MethodChannel? = null
    private var eventChannel: EventChannel? = null
    private var eventSink: EventChannel.EventSink? = null
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * NAME: configureFlutter
     *
     * DESCRIPTION:
     *     Configures Flutter channels at runtime when Flutter is available.
     *     This allows Android VPN service to work both with and without Flutter.
     *
     * PARAMETERS:
     *     binaryMessenger - Flutter binary messenger
     */
    fun configureFlutter(binaryMessenger: BinaryMessenger) {
        try {
            // Setup channels
            methodChannel = MethodChannel(binaryMessenger, METHOD_CHANNEL_NAME)
            eventChannel = EventChannel(binaryMessenger, EVENT_CHANNEL_NAME)

            // Setup channel handlers
            setupChannelHandlers()

            logInfo("Flutter channels configured at runtime")
        } catch (e: Exception) {
            logError("Failed to configure Flutter channels", e)
        }
    }

    /**
     * NAME: setupChannelHandlers
     *
     * DESCRIPTION:
     *     Sets up method and event channel handlers.
     */
    private fun setupChannelHandlers() {
        // Setup method channel handler
        methodChannel?.setMethodCallHandler { call, result ->
            coroutineScope.launch {
                handleMethodCall(call, result)
            }
        }

        // Setup event channel handler
        eventChannel?.setStreamHandler(this)

        logDebug("Platform Channel handlers configured")
    }

    /**
     * NAME: handleMethodCall
     *
     * DESCRIPTION:
     *     Handles Flutter method calls, equivalent to iOS handleMethodCall.
     *
     * PARAMETERS:
     *     call - Flutter method call
     *     result - Flutter result callback
     */
    private suspend fun handleMethodCall(call: MethodCall, result: MethodChannel.Result) {
        logDebug(
            message = "Handling method call",
            context = mapOf("method" to call.method)
        )

        try {
            when (call.method) {
                "initializeBackend" -> handleInitializeBackend(result)
                "login" -> handleLogin(call.arguments, result)
                "connect" -> handleConnect(call.arguments, result)
                "disconnect" -> handleDisconnect(result)
                "getStatus" -> handleGetStatus(result)
                "getServers" -> handleGetServers(result)
                "pingServer" -> handlePingServer(call.arguments, result)
                "pingServers" -> handlePingServers(result)
                "getInterface" -> handleGetInterfaceInfo(result)  // Flutter调用getInterface
                "getInterfaceInfo" -> handleGetInterfaceInfo(result)  // 保持兼容性
                "getRoutingSettings" -> handleGetRoutingSettings(result)
                "setRoutingSettings" -> handleSetRoutingSettings(call.arguments, result)
                "setServerProviderUrl" -> handleSetServerProviderUrl(call.arguments, result)
                "checkVPNPermission" -> handleCheckVPNPermission(result)
                "requestVPNPermission" -> handleRequestVPNPermission(result)
                "checkBatteryOptimization" -> handleCheckBatteryOptimization(result)
                "requestBatteryOptimization" -> handleRequestBatteryOptimization(result)
                "exportLogs" -> handleExportLogs(call.arguments, result)
                "health" -> handleHealthCheck(result)
                "forceResetConnectionState" -> handleForceResetConnectionState(call.arguments, result)
                else -> result.notImplemented()
            }
        } catch (e: Exception) {
            logError(
                message = "Method call failed",
                context = mapOf("method" to call.method),
                throwable = e
            )
            result.error(
                "METHOD_CALL_FAILED",
                "Failed to handle method call: ${e.message}",
                null
            )
        }
    }

    /**
     * NAME: handleInitializeBackend
     *
     * DESCRIPTION:
     *     Handles backend initialization request.
     *     Checks if VPN service is initialized and initializes if needed.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleInitializeBackend(result: MethodChannel.Result) {
        try {
            val isInitialized = vpnServiceInterface.isInitialized()
            if (!isInitialized) {
                vpnServiceInterface.initialize()
            }
            
            logInfo("Backend initialization completed successfully")
            result.success(true)
        } catch (e: Exception) {
            logError("Backend initialization failed", e)
            result.error(
                "BACKEND_INIT_FAILED",
                "Failed to initialize backend: ${e.message}",
                null
            )
        }
    }

    /**
     * NAME: handleLogin
     *
     * DESCRIPTION:
     *     Handles user login request.
     *
     * PARAMETERS:
     *     arguments - Method call arguments containing username and password
     *     result - Flutter result callback
     */
    private suspend fun handleLogin(arguments: Any?, result: MethodChannel.Result) {
        try {
            logInfo("Android login method called - starting processing")

            val args = arguments as? Map<*, *>
            val username = args?.get("username") as? String
            val password = args?.get("password") as? String

            if (username == null || password == null) {
                logError("Login failed: missing username or password")
                result.error("INVALID_ARGUMENTS", "Username and password are required", null)
                return
            }

            logInfo(
                message = "Processing login request",
                context = mapOf("username" to username)
            )

            val loginResult = vpnServiceInterface.login(username, password)

            if (loginResult.isSuccess) {
                // Return the login response data directly (already formatted by VPNServiceAdapter)
                val responseData = loginResult.getOrNull()
                result.success(responseData)
                logInfo("Login request completed successfully")
            } else {
                val exception = loginResult.exceptionOrNull()
                val errorMessage = exception?.message ?: "Authentication failed"
                val errorCode = when (exception) {
                    is VPNServiceError -> exception.errorCode.code.toString()
                    else -> "2000" // AUTH_INVALID_CREDENTIALS
                }

                logError("Login request failed", exception)

                // Return detailed error information to Flutter with numeric error code
                result.error(errorCode, errorMessage, mapOf<String, Any>(
                    "error_code" to (exception?.let {
                        if (it is VPNServiceError) it.errorCode.code else 2000
                    } ?: 2000),
                    "error_category" to (exception?.let {
                        if (it is VPNServiceError) it.errorCode.category else "Authentication"
                    } ?: "Authentication"),
                    "error_message" to errorMessage,
                    "timestamp" to (System.currentTimeMillis() / 1000)
                ))
            }
        } catch (e: Exception) {
            logError("Login failed", e)
            result.error("LOGIN_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleConnect
     *
     * DESCRIPTION:
     *     Handles VPN connection request.
     *
     * PARAMETERS:
     *     arguments - Method call arguments containing server_id
     *     result - Flutter result callback
     */
    private suspend fun handleConnect(arguments: Any?, result: MethodChannel.Result) {
        try {
            val args = arguments as? Map<*, *>
            val serverId = args?.get("server_id") as? String

            if (serverId == null) {
                result.error("INVALID_ARGUMENTS", "Server ID is required", null)
                return
            }

            logInfo(
                message = "Processing connect request",
                context = mapOf("server_id" to serverId)
            )

            val connectResult = vpnServiceInterface.connect(serverId)

            if (connectResult.isSuccess) {
                // Send interface info as event to Flutter
                val responseData = connectResult.getOrNull()
                if (responseData != null && responseData is Map<*, *>) {
                    val interfaceInfo = responseData["interface_info"]
                    if (interfaceInfo != null) {
                        sendEvent("interface_info", interfaceInfo)
                        logDebug("Interface info sent to Flutter via event")
                    }
                }

                // Return boolean success for Flutter compatibility
                result.success(true)
                logInfo("Connect request completed successfully")
            } else {
                // Get detailed error information
                val exception = connectResult.exceptionOrNull()
                val errorMessage = exception?.message ?: "Connection failed"
                val errorCode = when (exception) {
                    is VPNServiceError -> exception.errorCode.code.toString()
                    else -> "1004" // CONNECTION_FAILED
                }
                val errorType = when (exception) {
                    is VPNServiceError -> exception.getErrorType()
                    else -> "CONNECTION_FAILED"
                }

                logError("Connect request failed", exception)

                // Return detailed error information to Flutter with numeric error code
                result.error(errorCode, errorMessage, mapOf<String, Any>(
                    "error_type" to errorType,
                    "error_message" to errorMessage,
                    "error_code" to (exception?.let {
                        if (it is VPNServiceError) it.errorCode.code else 1004
                    } ?: 1004),
                    "error_category" to (exception?.let {
                        if (it is VPNServiceError) it.errorCode.category else "General"
                    } ?: "General"),
                    "timestamp" to (System.currentTimeMillis() / 1000)
                ))
            }
        } catch (e: Exception) {
            logError("Connect failed", e)
            result.error("CONNECTION_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleDisconnect
     *
     * DESCRIPTION:
     *     Handles VPN disconnection request.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleDisconnect(result: MethodChannel.Result) {
        try {
            logInfo("Processing disconnect request")

            val disconnectResult = vpnServiceInterface.disconnect()
            result.success(disconnectResult.isSuccess)
            
            logInfo("Disconnect request completed")
        } catch (e: Exception) {
            logError("Disconnect failed", e)
            result.error("DISCONNECTION_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleGetStatus
     *
     * DESCRIPTION:
     *     Handles status query request.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleGetStatus(result: MethodChannel.Result) {
        try {
            val status = vpnServiceInterface.getStatus()
            
            val response = mapOf(
                "success" to true,
                "data" to status.toFlutterMap()
            )

            result.success(response)
            logDebug("Status query completed")
        } catch (e: Exception) {
            logError("Status query failed", e)
            result.error("STATUS_QUERY_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleGetServers
     *
     * DESCRIPTION:
     *     Handles server list query request.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleGetServers(result: MethodChannel.Result) {
        try {
            val servers = vpnServiceInterface.getServers()

            val response = mapOf(
                "success" to true,
                "data" to servers.map { it.toMap() }
            )

            result.success(response)
            logDebug("Server list query completed")
        } catch (e: Exception) {
            logError("Server list query failed", e)
            result.error("SERVER_QUERY_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handlePingServer
     *
     * DESCRIPTION:
     *     Handles single server ping request.
     *
     * PARAMETERS:
     *     arguments - Method call arguments containing server_id
     *     result - Flutter result callback
     */
    private suspend fun handlePingServer(arguments: Any?, result: MethodChannel.Result) {
        val requestStartTime = System.currentTimeMillis()
        println("🔍 [PLATFORM_PING_SERVER] Single server ping request received - start_time: $requestStartTime, thread: ${Thread.currentThread().name}")

        try {
            val args = arguments as? Map<*, *>
            val serverId = args?.get("server_id") as? String

            if (serverId == null) {
                println("🔍 [PLATFORM_PING_SERVER_ERROR] Invalid arguments - server_id is null")
                result.error("INVALID_ARGUMENTS", "Server ID is required", null)
                return
            }

            println("🔍 [PLATFORM_PING_SERVER] Pinging server - server_id: $serverId")
            val pingStartTime = System.currentTimeMillis()
            val pingResult = vpnServiceInterface.pingServer(serverId)
            val pingTime = System.currentTimeMillis() - pingStartTime

            // Flutter UI expects direct int result, not wrapped in response object
            result.success(pingResult)

            val totalTime = System.currentTimeMillis() - requestStartTime
            println("🔍 [PLATFORM_PING_SERVER_SUCCESS] Single server ping completed - server_id: $serverId, result: ${pingResult}ms, ping_time: ${pingTime}ms, total_time: ${totalTime}ms")

            logDebug(
                message = "Ping server completed",
                context = mapOf(
                    "server_id" to serverId,
                    "result" to pingResult,
                    "ping_time_ms" to pingTime,
                    "total_time_ms" to totalTime,
                    "thread" to Thread.currentThread().name
                )
            )
        } catch (e: Exception) {
            val errorTime = System.currentTimeMillis() - requestStartTime
            println("🔍 [PLATFORM_PING_SERVER_ERROR] Single server ping FAILED - error: ${e.message}, total_time: ${errorTime}ms, thread: ${Thread.currentThread().name}")

            logError("Ping server failed", mapOf(
                "total_time_ms" to errorTime,
                "thread" to Thread.currentThread().name
            ), e)
            // Return -1 for ping failures as Flutter UI expects
            result.success(-1)
        }
    }

    /**
     * NAME: handlePingServers
     *
     * DESCRIPTION:
     *     Handles batch server ping request.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handlePingServers(result: MethodChannel.Result) {
        val requestStartTime = System.currentTimeMillis()
        println("🔍 [PLATFORM_PING_SERVERS] Ping servers request received - start_time: $requestStartTime, thread: ${Thread.currentThread().name}")

        try {
            val pingStartTime = System.currentTimeMillis()
            vpnServiceInterface.pingServers()
            val pingRequestTime = System.currentTimeMillis() - pingStartTime

            result.success(null)

            val totalTime = System.currentTimeMillis() - requestStartTime
            println("🔍 [PLATFORM_PING_SERVERS_SUCCESS] Ping servers request completed - ping_request_time: ${pingRequestTime}ms, total_time: ${totalTime}ms")

            logDebug("Ping servers request sent successfully", mapOf(
                "ping_request_time_ms" to pingRequestTime,
                "total_time_ms" to totalTime,
                "thread" to Thread.currentThread().name
            ))
        } catch (e: Exception) {
            val errorTime = System.currentTimeMillis() - requestStartTime
            println("🔍 [PLATFORM_PING_SERVERS_ERROR] Ping servers request FAILED - error: ${e.message}, total_time: ${errorTime}ms, thread: ${Thread.currentThread().name}")

            logError("Ping servers failed", mapOf(
                "total_time_ms" to errorTime,
                "thread" to Thread.currentThread().name
            ), e)
            result.error("PING_SERVERS_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleGetInterfaceInfo
     *
     * DESCRIPTION:
     *     Handles network interface info query request.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleGetInterfaceInfo(result: MethodChannel.Result) {
        try {
            val interfaceInfo = vpnServiceInterface.getInterfaceInfo()

            val response = mapOf(
                "success" to true,
                "data" to interfaceInfo.toFlutterMap()
            )

            result.success(response)
            logDebug("Interface info query completed")
        } catch (e: Exception) {
            logError("Interface info query failed", e)
            result.error("INTERFACE_QUERY_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleCheckVPNPermission
     *
     * DESCRIPTION:
     *     Handles VPN permission status check request.
     *     Provides permission status information to Flutter layer.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleCheckVPNPermission(result: MethodChannel.Result) {
        try {
            logInfo("Processing VPN permission check request")

            // 使用PermissionManager检查VPN权限状态
            val permissionManager = PermissionManager(context)
            val permissionResult = permissionManager.checkVPNPermissionStatus()

            val response = mapOf(
                "granted" to permissionResult.granted,
                "status" to permissionResult.status.value,
                "message" to permissionResult.userMessage
            )

            result.success(response)
            logInfo(
                message = "VPN permission check completed",
                context = mapOf(
                    "granted" to permissionResult.granted,
                    "status" to permissionResult.status.value
                )
            )
        } catch (e: Exception) {
            logError("VPN permission check failed", e)
            result.error("VPN_PERMISSION_CHECK_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleRequestVPNPermission
     *
     * DESCRIPTION:
     *     Handles VPN permission request from Flutter.
     *     Uses proper Activity context and startActivityForResult for permission callback.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleRequestVPNPermission(result: MethodChannel.Result) {
        try {
            logInfo("Processing VPN permission request")

            // 检查当前权限状态
            val permissionManager = PermissionManager(context)
            val currentStatus = permissionManager.checkVPNPermissionStatus()

            if (currentStatus.granted) {
                // 权限已授权
                val response = mapOf(
                    "granted" to true,
                    "status" to currentStatus.status.value,
                    "message" to "VPN权限已授权"
                )
                result.success(response)
                logInfo("VPN permission already granted")
                return
            }

            // 获取Activity上下文进行权限请求
            val activity = context as? android.app.Activity
            if (activity == null) {
                logError(
                    message = "Context is not an Activity, cannot request VPN permission",
                    context = mapOf("context_type" to (context::class.simpleName ?: "Unknown"))
                )
                val response = mapOf(
                    "granted" to false,
                    "status" to currentStatus.status.value,
                    "message" to "无法请求VPN权限：需要Activity上下文",
                    "requires_user_action" to true,
                    "guidance" to "请手动在系统设置中启用VPN权限"
                )
                result.success(response)
                return
            }

            logInfo(
                message = "Starting VPN permission request with Activity context",
                context = mapOf("activity_class" to (activity::class.simpleName ?: "Unknown"))
            )

            // 直接使用VpnService.prepare()进行权限请求，类似参考代码的实现
            try {
                val prepareIntent = android.net.VpnService.prepare(activity)
                if (prepareIntent != null) {
                    logInfo("VPN permission required, launching system dialog")

                    // 直接启动权限请求Activity
                    activity.startActivityForResult(prepareIntent, VPN_PERMISSION_REQUEST_CODE)

                    // 返回需要用户操作的状态
                    val response = mapOf(
                        "granted" to false,
                        "status" to currentStatus.status.value,
                        "message" to "VPN权限请求已启动，请在系统对话框中选择'允许'",
                        "requires_user_action" to true,
                        "guidance" to "系统已显示VPN权限请求对话框，请点击'允许'以授权VPN功能"
                    )
                    result.success(response)
                    logInfo("VPN permission dialog launched successfully")
                } else {
                    // 权限已授权
                    val response = mapOf(
                        "granted" to true,
                        "status" to "authorized",
                        "message" to "VPN权限已授权"
                    )
                    result.success(response)
                    logInfo("VPN permission already granted")
                }
            } catch (e: Exception) {
                logError("Failed to launch VPN permission request", e)
                val response = mapOf(
                    "granted" to false,
                    "status" to currentStatus.status.value,
                    "message" to "无法启动VPN权限请求：${e.message}",
                    "requires_user_action" to true,
                    "guidance" to "请手动在系统设置中启用VPN权限"
                )
                result.success(response)
            }

        } catch (e: Exception) {
            logError("VPN permission request failed", e)
            result.error("VPN_PERMISSION_REQUEST_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleCheckBatteryOptimization
     *
     * DESCRIPTION:
     *     Handles battery optimization status check request.
     *     Provides battery optimization whitelist status to Flutter layer.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleCheckBatteryOptimization(result: MethodChannel.Result) {
        try {
            logInfo("Processing battery optimization check request")

            val permissionManager = PermissionManager(context)
            val isIgnored = permissionManager.isBatteryOptimizationIgnored()

            val response = mapOf(
                "ignored" to isIgnored,
                "status" to if (isIgnored) "ignored" else "optimized",
                "message" to if (isIgnored) "应用已在电池优化白名单中" else "应用未在电池优化白名单中",
                "recommendation" to if (!isIgnored) "建议将应用加入电池优化白名单以确保VPN稳定运行" else null
            )

            result.success(response)
            logInfo("Battery optimization status checked", mapOf("ignored" to isIgnored))

        } catch (e: Exception) {
            logError("Battery optimization check failed", e)
            result.error("BATTERY_OPTIMIZATION_CHECK_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleRequestBatteryOptimization
     *
     * DESCRIPTION:
     *     Handles battery optimization exemption request from Flutter.
     *     Guides user to add app to battery optimization whitelist.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleRequestBatteryOptimization(result: MethodChannel.Result) {
        try {
            logInfo("Processing battery optimization exemption request")

            val permissionManager = PermissionManager(context)

            // Check current status
            if (permissionManager.isBatteryOptimizationIgnored()) {
                val response = mapOf(
                    "ignored" to true,
                    "status" to "ignored",
                    "message" to "应用已在电池优化白名单中"
                )
                result.success(response)
                logInfo("Battery optimization already ignored")
                return
            }

            // Get Activity context for permission request
            val activity = context as? android.app.Activity
            if (activity == null) {
                logError(
                    message = "Context is not an Activity, cannot request battery optimization",
                    context = mapOf("context_type" to (context::class.simpleName ?: "Unknown"))
                )
                val response = mapOf(
                    "ignored" to false,
                    "status" to "optimized",
                    "message" to "无法请求电池优化权限：需要Activity上下文",
                    "requires_user_action" to true,
                    "guidance" to "请手动在系统设置中将应用加入电池优化白名单"
                )
                result.success(response)
                return
            }

            logInfo(
                message = "Starting battery optimization exemption request with Activity context",
                context = mapOf("activity_class" to (activity::class.simpleName ?: "Unknown"))
            )

            // Request battery optimization exemption
            permissionManager.requestBatteryOptimizationExemption(activity)

            val response = mapOf(
                "ignored" to false,
                "status" to "optimized",
                "message" to "电池优化权限请求已启动，请在系统设置中允许",
                "requires_user_action" to true,
                "guidance" to "系统将打开电池优化设置页面，请将本应用加入白名单"
            )
            result.success(response)
            logInfo("Battery optimization exemption request launched successfully")

        } catch (e: Exception) {
            logError("Battery optimization request failed", e)
            result.error("BATTERY_OPTIMIZATION_REQUEST_FAILED", e.message, null)
        }
    }

    /**
     * NAME: checkBatteryOptimizationStatus
     *
     * DESCRIPTION:
     *     Checks battery optimization status and sends event to Flutter.
     *     Called from MainActivity after battery optimization permission result.
     */
    fun checkBatteryOptimizationStatus() {
        try {
            val permissionManager = PermissionManager(context)
            val isIgnored = permissionManager.isBatteryOptimizationIgnored()

            val eventData = mapOf(
                "ignored" to isIgnored,
                "status" to if (isIgnored) "ignored" else "optimized",
                "message" to if (isIgnored) "应用已加入电池优化白名单" else "应用仍在电池优化列表中"
            )

            sendEvent("battery_optimization_result", eventData)
            logInfo("Battery optimization status event sent", mapOf("ignored" to isIgnored))

        } catch (e: Exception) {
            logError("Failed to check battery optimization status", e)
        }
    }

    /**
     * NAME: handleHealthCheck
     *
     * DESCRIPTION:
     *     Handles health check request.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleHealthCheck(result: MethodChannel.Result) {
        try {
            val isHealthy = vpnServiceInterface.isInitialized() && vpnServiceInterface.isHealthy()

            val healthInfo = mapOf(
                "status" to "healthy",
                "version" to getAppVersion(),
                "vpn_status" to vpnServiceInterface.getStatus().flutterStatusString
            )

            val response = mapOf(
                "success" to isHealthy,
                "data" to healthInfo
            )

            result.success(response)
            logDebug(
                message = "Health check completed",
                context = mapOf("healthy" to isHealthy)
            )
        } catch (e: Exception) {
            logError("Health check failed", e)
            result.success(mapOf("success" to false))
        }
    }

    /**
     * NAME: getAppVersion
     *
     * DESCRIPTION:
     *     Gets application version string.
     *
     * RETURNS:
     *     String - Application version
     */
    private fun getAppVersion(): String {
        return try {
            "1.2.0" // TODO: Get from BuildConfig or PackageManager
        } catch (e: Exception) {
            "unknown"
        }
    }



    /**
     * NAME: handleGetRoutingSettings
     *
     * DESCRIPTION:
     *     Handles routing settings query request.
     *     Returns current routing configuration from memory.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleGetRoutingSettings(result: MethodChannel.Result) {
        try {
            logInfo("Processing get routing settings request")

            // Get current routing configuration from memory
            val settings = routingConfigurationManager.toFlutterSettingsMap()

            logInfo("Retrieved routing settings from memory", mapOf(
                "mode" to (settings["mode"] ?: "unknown"),
                "custom_routes" to (settings["custom_routes"] ?: "")
            ))

            result.success(settings)
            logDebug("Get routing settings completed")
        } catch (e: Exception) {
            logError("Get routing settings failed", e)
            result.error("GET_ROUTING_SETTINGS_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleSetRoutingSettings
     *
     * DESCRIPTION:
     *     Handles routing settings update request.
     *     Updates routing configuration in memory for VPN connection use.
     *     UI layer handles SharedPreferences persistence separately.
     *
     * PARAMETERS:
     *     arguments - Method call arguments containing routing settings
     *     result - Flutter result callback
     */
    private suspend fun handleSetRoutingSettings(arguments: Any?, result: MethodChannel.Result) {
        try {
            logInfo("Processing set routing settings request")

            val settings = arguments as? Map<*, *>
            if (settings == null) {
                logError("Invalid routing settings arguments: null")
                result.error("INVALID_ARGUMENTS", "Routing settings arguments cannot be null", null)
                return
            }

            logDebug(
                message = "Received routing settings",
                context = mapOf("settings" to settings.toString())
            )

            // Update routing configuration in memory
            val updateSuccess = routingConfigurationManager.updateFromFlutterSettings(settings)

            if (updateSuccess) {
                logInfo("Routing settings updated successfully in memory", mapOf(
                    "mode" to (settings["mode"] ?: "unknown"),
                    "custom_routes" to (settings["custom_routes"] ?: "")
                ))

                // Return success response matching iOS format
                val response = mapOf(
                    "success" to true,
                    "message" to "Routing settings updated successfully"
                )
                result.success(response)
            } else {
                logError("Failed to update routing settings in memory")
                result.error("UPDATE_FAILED", "Failed to update routing settings", null)
            }

            logInfo("Set routing settings completed")
        } catch (e: Exception) {
            logError("Set routing settings failed", e)
            result.error("SET_ROUTING_SETTINGS_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleSetServerProviderUrl
     *
     * DESCRIPTION:
     *     Handles set server provider URL request.
     *     Updates the server list URL and fetches new server list.
     *
     * PARAMETERS:
     *     arguments - Method call arguments containing URL
     *     result - Flutter result callback
     */
    private suspend fun handleSetServerProviderUrl(arguments: Any?, result: MethodChannel.Result) {
        try {
            logInfo("Android setServerProviderUrl method called - starting processing")

            val args = arguments as? Map<*, *>
            val url = args?.get("url") as? String

            if (url == null) {
                logError("Invalid server provider URL arguments: missing url")
                result.error("INVALID_ARGUMENTS", "Missing or invalid URL parameter", null)
                return
            }

            logDebug(
                message = "Received server provider URL",
                context = mapOf("url" to url)
            )

            // Update server list URL in ServerManager
            val setUrlResult = vpnServiceInterface.setServerListUrl(url)

            if (setUrlResult.isSuccess) {
                logInfo("Server provider URL updated successfully", mapOf("url" to url))

                // Return success response matching iOS format
                val response = mapOf(
                    "success" to true,
                    "message" to "Server provider URL updated successfully"
                )
                result.success(response)
            } else {
                val error = setUrlResult.exceptionOrNull()
                logError("Failed to set server provider URL", mapOf("url" to url), error)

                // 将Android错误映射到统一的错误码体系
                val (errorCode, errorMessage) = mapServerListError(error)
                result.error(errorCode, errorMessage, mapOf(
                    "error_code" to when(errorCode) {
                        "HTTP_ERROR" -> when {
                            errorMessage.contains("404") -> 1201
                            errorMessage.contains("408") || errorMessage.contains("timeout") -> 1203
                            errorMessage.contains("500") || errorMessage.contains("502") || errorMessage.contains("503") -> 1204
                            else -> 1204
                        }
                        "INVALID_ARGUMENTS" -> 1202
                        else -> 1204
                    },
                    "error_category" to "ServerList",
                    "timestamp" to (System.currentTimeMillis() / 1000)
                ))
            }

        } catch (e: Exception) {
            logError("Set server provider URL failed", e)
            result.error("SET_SERVER_PROVIDER_URL_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleLogout
     *
     * DESCRIPTION:
     *     Handles user logout request.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleLogout(result: MethodChannel.Result) {
        try {
            logInfo("Processing logout request")

            // TODO: Implement logout logic
            // For now, just return success
            result.success(null)

            logInfo("Logout request completed")
        } catch (e: Exception) {
            logError("Logout failed", e)
            result.error("LOGOUT_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleReconnect
     *
     * DESCRIPTION:
     *     Handles VPN reconnection request.
     *     Android platform relies on Flutter UI ReconnectService for reconnection logic.
     *     This method is not implemented as reconnection is handled by UI layer.
     *
     * PARAMETERS:
     *     result - Flutter result callback
     */
    private suspend fun handleReconnect(result: MethodChannel.Result) {
        try {
            logInfo("Processing reconnect request")

            // Android platform relies on Flutter UI ReconnectService for reconnection logic
            // Reconnection is handled through disconnect -> connect flow in UI layer
            result.success(null)

            logInfo("Reconnect request completed - handled by UI layer")
        } catch (e: Exception) {
            logError("Reconnect failed", e)
            result.error("RECONNECT_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleForceResetConnectionState
     *
     * DESCRIPTION:
     *     Handles force reset connection state request from Flutter.
     *     This is a recovery method for when the connection state gets stuck.
     *
     * PARAMETERS:
     *     arguments - Method call arguments (optional reason parameter)
     *     result - Flutter result callback
     */
    private suspend fun handleForceResetConnectionState(arguments: Any?, result: MethodChannel.Result) {
        try {
            val args = arguments as? Map<*, *>
            val reason = args?.get("reason") as? String ?: "Flutter UI requested reset"

            logInfo("Processing force reset connection state request", mapOf(
                "reason" to reason
            ))

            // Get connection manager and force reset state
            val connectionManager = vpnServiceInterface.getConnectionManager()
            if (connectionManager != null) {
                connectionManager.forceResetConnectionState(reason)
                result.success(true)
                logInfo("Force reset connection state completed successfully")
            } else {
                result.error("CONNECTION_MANAGER_NOT_AVAILABLE", "Connection manager not available", null)
                logError("Connection manager not available for force reset")
            }
        } catch (e: Exception) {
            logError("Force reset connection state failed", e)
            result.error("FORCE_RESET_FAILED", e.message, null)
        }
    }

    /**
     * NAME: handleExportLogs
     *
     * DESCRIPTION:
     *     Handles log export request from Flutter.
     *     Exports logs to JSON format and returns the file path.
     *
     * PARAMETERS:
     *     arguments - Method call arguments (optional format parameter)
     *     result - Flutter result callback
     */
    private suspend fun handleExportLogs(arguments: Any?, result: MethodChannel.Result) {
        try {
            logInfo("Processing log export request")

            val args = arguments as? Map<*, *>
            val format = args?.get("format") as? String ?: "json"

            // Get LoggingManager instance
            val loggingManager = com.panabit.client.infrastructure.logging.LoggingManager.getInstance(context)

            // Export logs using LoggingManager
            val exportPath = exportLogsToFile(loggingManager, format)

            if (exportPath.isNotEmpty()) {
                result.success(exportPath)
                logInfo("Log export completed successfully", mapOf("path" to exportPath))
            } else {
                result.error("EXPORT_FAILED", "Failed to export logs", null)
                logError("Log export failed - empty path returned")
            }
        } catch (e: Exception) {
            logError("Log export failed", e)
            result.error("EXPORT_FAILED", e.message, null)
        }
    }



    // MARK: - Event Channel Implementation

    /**
     * NAME: onListen
     *
     * DESCRIPTION:
     *     Called when Flutter starts listening to event stream.
     *     Equivalent to iOS onListen implementation.
     *
     * PARAMETERS:
     *     arguments - Stream arguments
     *     events - Event sink for sending events to Flutter
     */
    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
        logInfo("Flutter event stream connected")

        // Set event sink for VPNStateEventSender to enable reconnect events
        try {
            val connectionManager = vpnServiceInterface.getConnectionManager()
            connectionManager?.getVPNStateEventSender()?.setEventSink(events)
            logInfo("VPNStateEventSender event sink configured")
        } catch (e: Exception) {
            logError("Failed to configure VPNStateEventSender event sink", e)
        }

        // Send initial status (equivalent to iOS sendInitialStatus)
        coroutineScope.launch {
            sendInitialStatus()
        }
    }

    /**
     * NAME: onCancel
     *
     * DESCRIPTION:
     *     Called when Flutter stops listening to event stream.
     *
     * PARAMETERS:
     *     arguments - Stream arguments
     */
    override fun onCancel(arguments: Any?) {
        eventSink = null

        // Clear event sink for VPNStateEventSender
        try {
            val connectionManager = vpnServiceInterface.getConnectionManager()
            connectionManager?.getVPNStateEventSender()?.setEventSink(null)
            logInfo("VPNStateEventSender event sink cleared")
        } catch (e: Exception) {
            logError("Failed to clear VPNStateEventSender event sink", e)
        }

        logInfo("Flutter event stream disconnected")
    }

    /**
     * NAME: sendEvent
     *
     * DESCRIPTION:
     *     Sends event to Flutter through event channel.
     *     Equivalent to iOS sendEvent implementation.
     *
     * PARAMETERS:
     *     type - Event type (matching iOS event types)
     *     data - Event data
     */
    fun sendEvent(type: String, data: Any) {
        // Ensure event sending happens on main thread for Flutter compatibility
        coroutineScope.launch {
            withContext(Dispatchers.Main) {
                val eventSink = <EMAIL>
                if (eventSink == null) {
                    logDebug(
                        message = "No event sink available, skipping event",
                        context = mapOf("type" to type)
                    )
                    return@withContext
                }

                val event = mapOf(
                    "event" to type,
                    "data" to data
                )

                try {
                    eventSink.success(event)
                    logDebug(
                        message = "Event sent to Flutter",
                        context = mapOf("type" to type)
                    )
                } catch (e: Exception) {
                    logError(
                        message = "Failed to send event to Flutter",
                        context = mapOf("type" to type),
                        throwable = e
                    )
                }
            }
        }
    }

    /**
     * NAME: sendInitialStatus
     *
     * DESCRIPTION:
     *     Sends initial status event when Flutter connects.
     *
     */
    private suspend fun sendInitialStatus() {
        try {
            val status = vpnServiceInterface.getStatus()
            sendEvent("status", status.toFlutterMap())
            logInfo("Initial status sent to Flutter")
        } catch (e: Exception) {
            logError("Failed to send initial status", e)
        }
    }

    /**
     * NAME: sendStatusEvent
     *
     * DESCRIPTION:
     *     Sends VPN status change event to Flutter.
     *
     * PARAMETERS:
     *     status - VPN status object
     */
    fun sendStatusEvent(status: Any) {
        // Convert status to Flutter-compatible map format
        val statusData = when (status) {
            is VPNState -> status.toFlutterMap()
            else -> mapOf("state" to "unknown")
        }

        sendEvent("status", statusData)
        logInfo("Status event sent to Flutter")
    }

    /**
     * NAME: sendPingResultsEvent
     *
     * DESCRIPTION:
     *     Sends ping results event to Flutter.
     *
     * PARAMETERS:
     *     servers - List of servers with ping results
     */
    fun sendPingResultsEvent(servers: List<Any>) {
        val serverData = servers.map { server ->
            when (server) {
                is ServerInfo -> server.toMap()
                else -> emptyMap<String, Any>()
            }
        }

        sendEvent("ping_results", mapOf("servers" to serverData))
        logInfo(
            message = "Ping results event sent to Flutter",
            context = mapOf("server_count" to serverData.size)
        )
    }

    /**
     * NAME: sendPingCompleteEvent
     *
     * DESCRIPTION:
     *     Sends ping complete event to Flutter.
     */
    fun sendPingCompleteEvent() {
        sendEvent("ping_complete", emptyMap<String, Any>())
        logInfo("Ping complete event sent to Flutter")
    }

    /**
     * NAME: sendInterfaceInfoEvent
     *
     * DESCRIPTION:
     *     Sends network interface info event to Flutter.
     *
     * PARAMETERS:
     *     interfaceInfo - Network interface information
     */
    fun sendInterfaceInfoEvent(interfaceInfo: Any) {
        val interfaceData = when (interfaceInfo) {
            is com.panabit.client.domain.interfaces.InterfaceInfo -> interfaceInfo.toFlutterMap()
            else -> emptyMap<String, Any>()
        }

        sendEvent("interface_info", interfaceData)
        logDebug("Interface info event sent to Flutter")
    }

    /**
     * NAME: sendConnectionServerEvent
     *
     * DESCRIPTION:
     *     Sends connected server info event to Flutter.
     *
     * PARAMETERS:
     *     server - Connected server information
     */
    fun sendConnectionServerEvent(server: Any) {
        val serverData = when (server) {
            is ServerInfo -> server.toMap()
            else -> emptyMap<String, Any>()
        }

        val connectionData = mapOf(
            "server" to serverData,
            "timestamp" to (System.currentTimeMillis() / 1000)
        )

        sendEvent("conn_server", connectionData)
        logInfo("Connection server event sent to Flutter")
    }

    /**
     * NAME: sendTrafficStatisticsEvent
     *
     * DESCRIPTION:
     *     Sends traffic statistics event to Flutter.
     *     Matches iOS implementation format exactly.
     *
     * PARAMETERS:
     *     trafficData - Traffic statistics data map
     */
    fun sendTrafficStatisticsEvent(trafficData: Map<String, Any>) {
        sendEvent("traffic", trafficData)
        logDebug("Traffic statistics event sent to Flutter", mapOf(
            "upload_speed" to (trafficData["upload_speed"] ?: 0),
            "download_speed" to (trafficData["download_speed"] ?: 0),
            "total_upload" to (trafficData["total_upload"] ?: 0),
            "total_download" to (trafficData["total_download"] ?: 0)
        ))
    }

    // MARK: - Public API for MainActivity

    /**
     * NAME: handleInitializeBackend
     *
     * DESCRIPTION:
     *     Handles backend initialization request from MainActivity.
     *     Checks if VPN service is initialized and initializes if needed.
     *     Used by delayed initialization pattern.
     *
     * RETURNS:
     *     Boolean - true if initialization successful, false otherwise
     */
    suspend fun handleInitializeBackend(): Boolean {
        return try {
            val isInitialized = vpnServiceInterface.isInitialized()
            if (!isInitialized) {
                val initResult = vpnServiceInterface.initialize()
                if (initResult.isFailure) {
                    logError("VPN service initialization failed", initResult.exceptionOrNull())
                    return false
                }
            }

            logInfo("Backend initialization completed successfully")
            true
        } catch (e: Exception) {
            logError("Backend initialization failed", e)
            false
        }
    }

    /**
     * NAME: handleActivityResult
     *
     * DESCRIPTION:
     *     Handles activity results for permission requests.
     *     Forwards results to PermissionManager for proper callback handling.
     *
     * PARAMETERS:
     *     requestCode - Request code from activity result
     *     resultCode - Result code from activity result
     */
    fun handleActivityResult(requestCode: Int, resultCode: Int) {
        try {
            val permissionManager = PermissionManager(context)
            permissionManager.handleActivityResult(requestCode, resultCode)

            logDebug(
                message = "Activity result forwarded to PermissionManager",
                context = mapOf(
                    "requestCode" to requestCode,
                    "resultCode" to resultCode
                )
            )
        } catch (e: Exception) {
            logError("Error handling activity result", e)
        }
    }

    /**
     * NAME: cleanup
     *
     * DESCRIPTION:
     *     Cleans up resources and cancels ongoing operations.
     *     Should be called when the handler is no longer needed.
     */
    fun cleanup() {
        try {
            // Cancel all ongoing coroutines
            coroutineScope.cancel()

            // Clear event sink
            eventSink = null

            // Clear channel references
            methodChannel?.setMethodCallHandler(null)
            eventChannel?.setStreamHandler(null)
            methodChannel = null
            eventChannel = null

            logInfo("PlatformChannelHandler cleanup completed")
        } catch (e: Exception) {
            logError("Error during PlatformChannelHandler cleanup", e)
        }
    }

    /**
     * NAME: exportLogsToFile
     *
     * DESCRIPTION:
     *     Export logs to file using LoggingManager.
     *     Creates a JSON or text export file with all available logs.
     *
     * PARAMETERS:
     *     loggingManager - LoggingManager instance
     *     format - Export format ("json" or "text")
     *
     * RETURNS:
     *     String - Path to exported file, empty if failed
     */
    private suspend fun exportLogsToFile(
        loggingManager: com.panabit.client.infrastructure.logging.LoggingManager,
        format: String
    ): String = withContext(Dispatchers.IO) {
        try {
            // Get log directory from LoggingManager
            val logDirectory = java.io.File(context.filesDir, "logs")
            if (!logDirectory.exists()) {
                logError("Log directory does not exist: ${logDirectory.absolutePath}")
                return@withContext ""
            }

            // Create export file with timestamp
            val timestamp = System.currentTimeMillis()
            val exportFileName = "export_$timestamp.$format"
            val exportFile = java.io.File(logDirectory, exportFileName)

            // Get all log files (支持多种命名格式以确保兼容性)
            val logFiles: Array<java.io.File> = logDirectory.listFiles { file ->
                (file.name.startsWith("panabit") || file.name.startsWith("itforce_wan") || file.name.startsWith("itforce_")) && file.name.endsWith(".log")
            }?.sortedBy { it.lastModified() }?.toTypedArray() ?: emptyArray()

            if (logFiles.isEmpty()) {
                logWarn("No log files found for export")
                return@withContext ""
            }

            // Export based on format
            when (format) {
                "json" -> exportLogsAsJson(logFiles, exportFile)
                else -> exportLogsAsText(logFiles, exportFile)
            }

            logInfo("Logs exported successfully", mapOf(
                "file_count" to logFiles.size,
                "export_path" to exportFile.absolutePath,
                "format" to format
            ))

            exportFile.absolutePath
        } catch (e: Exception) {
            logError("Failed to export logs", e)
            ""
        }
    }

    /**
     * NAME: exportLogsAsJson
     *
     * DESCRIPTION:
     *     Export logs as JSON format.
     *
     * PARAMETERS:
     *     logFiles - Array of log files to export
     *     exportFile - Target export file
     */
    private suspend fun exportLogsAsJson(logFiles: Array<java.io.File>, exportFile: java.io.File) = withContext(Dispatchers.IO) {
        try {
            val allLogs = mutableListOf<Map<String, Any>>()

            logFiles.forEach { logFile ->
                logFile.readLines().forEach { line ->
                    val logEntry = parseLogLine(line)
                    if (logEntry != null) {
                        allLogs.add(logEntry)
                    }
                }
            }

            // Convert to JSON and write to file
            val jsonString = org.json.JSONArray(allLogs).toString(2)
            exportFile.writeText(jsonString)

            logInfo("JSON export completed", mapOf("entries" to allLogs.size))
        } catch (e: Exception) {
            logError("Failed to export logs as JSON", e)
            throw e
        }
    }

    /**
     * NAME: exportLogsAsText
     *
     * DESCRIPTION:
     *     Export logs as plain text format.
     *
     * PARAMETERS:
     *     logFiles - Array of log files to export
     *     exportFile - Target export file
     */
    private suspend fun exportLogsAsText(logFiles: Array<java.io.File>, exportFile: java.io.File) = withContext(Dispatchers.IO) {
        try {
            exportFile.bufferedWriter().use { writer ->
                logFiles.forEach { logFile ->
                    writer.appendLine("=== ${logFile.name} ===")
                    logFile.readLines().forEach { line ->
                        writer.appendLine(line)
                    }
                    writer.appendLine()
                }
            }

            logInfo("Text export completed")
        } catch (e: Exception) {
            logError("Failed to export logs as text", e)
            throw e
        }
    }

    /**
     * NAME: parseLogLine
     *
     * DESCRIPTION:
     *     Parse a log line into structured data.
     *     Expected format: [timestamp] level [tag] message
     *
     * PARAMETERS:
     *     line - Log line to parse
     *
     * RETURNS:
     *     Map<String, Any>? - Parsed log entry or null if parsing fails
     */
    private fun parseLogLine(line: String): Map<String, Any>? {
        try {
            // Parse format: [timestamp] level [tag] message (匹配LoggingManager的formatLogEntry格式)
            val regex = Regex("""^\[([^\]]+)\]\s+(\w+)\s+\[([^\]]+)\]\s+(.+)$""")
            val matchResult = regex.find(line)

            return if (matchResult != null) {
                val (timestamp, level, tag, message) = matchResult.destructured

                // 检查是否有错误信息（与iOS保持一致）
                var actualMessage = message
                var error: String? = null
                if (message.contains(" - Error: ")) {
                    val parts = message.split(" - Error: ", limit = 2)
                    actualMessage = parts[0]
                    error = parts[1]
                }

                val result = mutableMapOf<String, Any>(
                    "timestamp" to timestamp,
                    "level" to level.trim().lowercase(),
                    "module" to tag.trim(),
                    "message" to actualMessage
                )

                if (error != null) {
                    result["error"] = error
                }

                result
            } else {
                // If parsing fails, return as raw message
                mapOf(
                    "timestamp" to java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", java.util.Locale.getDefault()).format(java.util.Date()),
                    "level" to "info",
                    "module" to "unknown",
                    "message" to line
                )
            }
        } catch (e: Exception) {
            logError("Failed to parse log line: $line", e)
            return null
        }
    }

    /**
     * NAME: mapServerListError
     *
     * DESCRIPTION:
     *     Maps server list related errors to unified error codes.
     *     Provides consistent error handling across platforms.
     *
     * PARAMETERS:
     *     error - The exception that occurred
     *
     * RETURNS:
     *     Pair<String, String> - Error code and error message
     */
    private fun mapServerListError(error: Throwable?): Pair<String, String> {
        val errorMessage = error?.message ?: "Unknown error"

        return when {
            // HTTP 404 - Server list not found
            errorMessage.contains("404") || errorMessage.contains("Not Found") -> {
                Pair("HTTP_ERROR", "Server returned status code 404")
            }
            // HTTP 408 - Request timeout
            errorMessage.contains("408") || errorMessage.contains("timeout") -> {
                Pair("HTTP_ERROR", "Server returned status code 408")
            }
            // HTTP 500/502/503 - Server errors
            errorMessage.contains("500") || errorMessage.contains("502") || errorMessage.contains("503") -> {
                Pair("HTTP_ERROR", "Server returned status code ${extractStatusCode(errorMessage)}")
            }
            // JSON parsing errors
            errorMessage.contains("parse") || errorMessage.contains("JSON") || errorMessage.contains("format") -> {
                Pair("INVALID_ARGUMENTS", "Invalid server list format: $errorMessage")
            }
            // Network connection errors
            errorMessage.contains("network") || errorMessage.contains("connection") || errorMessage.contains("unreachable") -> {
                Pair("HTTP_ERROR", "Network error during server list fetch: $errorMessage")
            }
            // Default case
            else -> {
                Pair("HTTP_ERROR", "Server list fetch failed: $errorMessage")
            }
        }
    }

    /**
     * NAME: extractStatusCode
     *
     * DESCRIPTION:
     *     Extracts HTTP status code from error message.
     *
     * PARAMETERS:
     *     message - Error message containing status code
     *
     * RETURNS:
     *     String - Extracted status code or "unknown"
     */
    private fun extractStatusCode(message: String): String {
        val regex = Regex("(\\d{3})")
        return regex.find(message)?.value ?: "unknown"
    }
}

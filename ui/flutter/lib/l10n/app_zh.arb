{"@@locale": "zh", "appTitle": "Panabit iWAN", "login": "登录", "username": "用户名", "password": "密码", "domain": "域名", "rememberCredentials": "记住凭据", "loginButton": "登录", "loginFailed": "登录失败", "loginSuccess": "登录成功", "logout": "退出登录", "connect": "连接", "disconnect": "断开连接", "connecting": "连接中...", "connected": "已连接", "disconnected": "未连接", "disconnecting": "断开中...", "reconnecting": "重连中...", "connectionFailed": "连接失败", "connectionSuccess": "连接成功", "serverList": "服务器列表", "selectServer": "选择服务器", "noServersAvailable": "没有可用服务器", "refreshServers": "刷新服务器", "settings": "设置", "about": "关于", "statistics": "统计", "logs": "日志", "language": "语言", "theme": "主题", "autoConnect": "自动连接", "minimizeToTray": "最小化到托盘", "startWithSystem": "开机启动", "connectionTime": "连接时间", "dataTransferred": "数据传输", "ping": "延迟", "latency": "延迟", "version": "版本", "copyright": "版权", "termsOfService": "服务条款", "privacyPolicy": "隐私政策", "acceptLicense": "我接受服务条款和隐私政策", "licenseRequired": "您必须接受许可协议才能继续", "error": "错误", "warning": "警告", "info": "信息", "success": "成功", "cancel": "取消", "ok": "确定", "yes": "是", "no": "否", "save": "保存", "close": "关闭", "exit": "退出", "minimize": "最小化", "maximize": "最大化", "restore": "还原", "copy": "复制", "paste": "粘贴", "cut": "剪切", "selectAll": "全选", "clear": "清除", "refresh": "刷新", "retry": "重试", "loading": "加载中...", "pleaseWait": "请稍候...", "networkError": "网络错误", "serverError": "服务器错误", "connectionTimeout": "连接超时", "invalidCredentials": "凭据无效", "sessionExpired": "会话已过期", "accessDenied": "访问被拒绝", "serviceUnavailable": "服务不可用", "mainScreen": "主页", "connectionScreen": "连接", "userScreen": "用户", "settingsScreen": "设置", "aboutScreen": "关于", "logsScreen": "日志", "statisticsScreen": "统计", "enterServerDomain": "输入服务器域名", "serverDomain": "服务器域名", "serverDomainHint": "例如: vpn.example.com", "nextStep": "下一步", "clientDomain": "客户域", "change": "更改", "rememberUsernamePassword": "记住用户名和密码", "pleaseEnterServerDomain": "请输入服务器域名", "pleaseEnterUsername": "请输入用户名", "pleaseEnterPassword": "请输入密码", "pleaseEnterClientDomain": "请输入客户域名", "clientDomainHint": "例如: research.staff.unisase", "lookupServiceError": "查询服务器地址失败", "lookupServiceTimeout": "查询服务器地址超时", "lookupServiceInvalidResponse": "服务器返回无效响应", "lookupServiceNetworkError": "网络连接失败，请检查网络设置", "queryingServerAddress": "正在查询服务器地址...", "startingBackendService": "正在启动后端服务...", "backendServiceStartFailed": "启动后端服务失败，请检查权限或重启应用", "checkingBackendServiceStatus": "启动中...", "backendServiceHealthCheckFailed": "后端服务健康检查失败，请检查端口是否被占用或重启应用程序", "autoStart": "开机自启动", "autoStartEnabled": "开机自启动已开启", "autoStartDisabled": "开机自启动已关闭", "routingSettings": "路由设置", "applying": "应用中...", "applySettings": "应用设置", "settingsAppliedSuccessfully": "设置已成功应用", "noChangesToApply": "设置无变更，无需应用", "getRoutingSettingsFailed": "获取路由设置失败", "saveAutoStartSettingFailed": "保存自动启动设置失败", "saveRoutingSettingsFailed": "保存路由设置失败", "appSettings": "应用设置", "appName": "应用名称", "versionNumber": "版本号", "deviceId": "设备ID", "gettingDeviceId": "获取中...", "getDeviceIdFailed": "获取失败", "agreementsAndContact": "协议条款", "viewTermsOfService": "查看用户服务协议", "viewPrivacyPolicy": "查看隐私保护政策", "officialWebsite": "官方网站", "technicalSupport": "技术支持", "openLinkFailed": "打开链接失败", "clickToView": "点击查看", "sendEmailFailed": "发送邮件失败", "vpnClientFeedback": "WAN客户端反馈", "allUrlLaunchMethodsFailed": "所有URL启动方法都失败了", "openLinkFailedWithError": "打开链接失败", "sendEmailFailedWithError": "发送邮件失败", "statisticsInfo": "统计信息", "status": "状态", "interface": "接口", "upload": "上行", "download": "下行", "localIp": "本地IP", "itforceIp": "云IP", "personalInfo": "个人信息", "editPersonalInfo": "编辑个人信息", "name": "姓名", "pleaseEnterName": "请输入姓名", "department": "部门", "position": "职位", "accountInfo": "账户信息", "clientDomainLabel": "客户域", "usernameLabel": "用户名", "deviceInfo": "设备ID", "logoutButton": "注销", "personalInfoSaved": "个人信息已保存", "saveFailed": "保存失败，请重试", "notSet": "未设置", "editUserInfo": "编辑个人信息", "confirmLogout": "确认注销", "logoutConfirmation": "确定要注销吗？", "logoutWithVpnWarning": "确定要注销吗？\n\n注意：当前WAN连接将会自动断开。", "disconnectAndExit": "断开并退出", "clearLogs": "清除日志", "confirmClearLogs": "确定要清除所有日志吗？", "confirm": "确定", "confirmReconnection": "确认重连", "routingChangeRequiresReconnection": "当前VPN已连接，更改路由设置需要重新连接，是否确认并重连？", "confirmAndReconnect": "确认并重连", "routingSettingsAppliedAndReconnected": "路由设置已保存并重新连接", "routingSettingsAppliedDisconnected": "路由设置已保存，VPN已断开", "routingSettingsReconnectionFailed": "路由设置重连失败", "logsExportedTo": "日志已导出到", "exportLogsFailed": "导出日志失败", "logCopiedToClipboard": "日志已复制到剪贴板", "allLevels": "所有级别", "searchLogs": "搜索日志...", "logsTitle": "日志", "closeSearch": "关闭搜索", "searchLogsTooltip": "搜索日志", "filterByLevel": "按级别筛选", "moreActions": "更多操作", "exportLogs": "导出日志", "filterPrefix": "筛选: ", "clearFilter": "清除筛选", "noLogs": "没有日志", "noData": "暂无数据", "scrollToLatestLog": "滚动到最新日志", "connectionManagement": "连接管理", "userInfo": "用户信息", "trafficStatistics": "流量统计", "systemLogs": "系统日志", "aboutApp": "关于应用", "connection": "连接", "user": "用户", "statisticsNav": "统计", "settingsNav": "设置", "logsNav": "日志", "aboutNav": "关于", "auto": "自动", "statusUpdated": "状态已更新", "routingMode": "路由模式", "allRouting": "全部路由", "allRoutingDescription": "所有流量都通过WAN隧道", "customRouting": "按网段路由", "customRoutingDescription": "仅指定网段的流量通过WAN隧道", "enterNetworkSegments": "请输入需要路由的网段", "networkSegmentsExample": "多个网段用逗号分隔，例如: ***********/16,10.0.0.0/8", "enterNetworkSegmentsHint": "输入网段...", "ensureCorrectCidrFormat": "请确保输入正确的CIDR格式", "uploadSpeed": "上行速率", "downloadSpeed": "下行速率", "unreachable": "不可达", "excellent": "极佳", "good": "良好", "poor": "较差", "languageSettings": "语言设置", "selectLanguage": "选择语言", "chinese": "中文", "english": "English", "languageChanged": "语言已更改", "pleaseSelectServer": "请先选择服务器", "connectingToServer": "正在连接...", "disconnectingFromServer": "正在断开连接...", "connectionTimeoutDetailed": "连接超时，请检查网络连接或稍后重试", "connectionFailedGeneric": "连接失败", "disconnectedFromServer": "已断开连接", "switchingToServer": "正在切换到 {serverName}...", "connectedToServer": "已连接到 {serverName}", "currentlyConnectedTo": "当前连接到 {serverName}，正在切换到 {newServerName}", "selectServerFirst": "请先选择服务器", "operationTimeout": "操作超时，请稍后重试", "pingServersFailed": "测试延迟失败: {error}", "networkConnectionFailed": "网络连接失败，请检查网络设置", "realtimeConnectionInterrupted": "实时连接中断，正在尝试重新连接...", "authenticationFailed": "认证失败，请重新登录", "operationFailedRetry": "操作失败，请稍后重试", "systemTrayConnected": "已连接", "systemTrayConnecting": "正在连接...", "systemTrayDisconnecting": "正在断开...", "systemTrayDisconnected": "未连接", "showWindow": "显示窗口", "hideWindow": "隐藏窗口", "exitApp": "退出应用", "processing": "处理中...", "calculating": "计算中...", "operationCancelled": "操作已取消", "testLatency": "测试延迟", "testingLatency": "正在测试延迟...", "latencyTestComplete": "延迟测试完成", "currentlyConnectedToServer": "当前连接到: {serverName}", "unknownServer": "未知服务器", "noAutoServersAvailable": "没有可用的自动分流服务器，请检查网络连接或联系管理员", "autoServerSelectionFailed": "自动服务器选择失败，请手动选择服务器或稍后重试", "apiInvalidRequest": "无效的请求格式或参数", "apiInvalidCredentials": "用户名或密码错误", "apiServerError": "服务器内部错误", "apiResourceNotFound": "请求的资源不存在", "apiUnauthorized": "未授权访问，请重新登录", "apiForbidden": "禁止访问该资源", "apiTimeout": "请求超时，请稍后重试", "apiConflict": "资源冲突", "apiRateLimit": "请求过于频繁，请稍后重试", "apiGatewayError": "网关错误", "apiServiceUnavailable": "服务暂时不可用，请稍后重试", "networkUnreachable": "网络不可达，请检查网络连接", "networkDnsFailure": "DNS解析失败，请检查服务器地址", "networkConnectionReset": "网络连接被重置", "networkConnectionClosed": "网络连接已关闭", "networkProxyError": "代理服务器错误", "networkTlsError": "TLS/SSL错误", "authInvalidCredentials": "无效的用户凭据", "authExpiredCredentials": "凭据已过期，请重新登录", "authRateLimit": "认证请求过于频繁，请稍后重试", "authAccountLocked": "账户已锁定，请联系管理员", "authInvalidToken": "无效的认证令牌", "authTokenExpired": "认证令牌已过期，请重新登录", "authMissingCredentials": "缺少认证凭据", "tunnelError": "隧道错误", "tunnelInitFailed": "隧道初始化失败", "tunnelCloseFailed": "隧道关闭失败", "tunnelReadFailed": "隧道读取失败", "tunnelWriteFailed": "隧道写入失败", "tunnelConfigFailed": "隧道配置失败", "configError": "配置错误", "configInvalid": "无效的配置", "configFileNotFound": "配置文件不存在", "configFileReadFailed": "配置文件读取失败", "configFileWriteFailed": "配置文件写入失败", "configFileParseFailed": "配置文件解析失败", "platformError": "平台错误", "platformUnsupported": "不支持的平台", "platformInitFailed": "平台初始化失败", "platformIoError": "平台IO错误", "platformPermissionDenied": "权限被拒绝，请以管理员身份运行", "domainLookupFailed": "域名查找失败", "domainNotFound": "域名不存在，请检查域名是否正确", "domainInvalid": "域名格式无效", "domainRequired": "域名参数不能为空", "domainLookupTimeout": "域名查找超时，请稍后重试", "domainLookupNetworkError": "域名查找网络错误，请检查网络连接", "serverListNotFound": "服务器列表未找到，请检查服务器地址", "serverListInvalid": "服务器列表格式无效", "serverListTimeout": "服务器列表请求超时，请稍后重试", "serverListNetworkError": "获取服务器列表时网络错误，请检查网络连接", "protocolError": "协议错误", "protocolInvalid": "无效的协议", "protocolUnsupported": "不支持的协议", "protocolVersionMismatch": "协议版本不匹配", "protocolHandshakeFailed": "协议握手失败", "protocolEncryptionFailed": "协议加密失败", "protocolDecryptionFailed": "协议解密失败", "unknownError": "未知错误", "invalidParameter": "无效参数", "notImplemented": "功能未实现", "permissionDenied": "权限被拒绝", "checkUpdate": "检查更新", "checkingUpdate": "检查更新中...", "updateAvailable": "发现新版本", "updateNotAvailable": "已是最新版本", "currentVersion": "当前版本", "latestVersion": "最新版本", "updateNow": "立即更新", "updateLater": "稍后提醒", "skipUpdate": "跳过更新", "downloading": "下载中...", "downloadProgress": "下载进度", "downloadComplete": "下载完成", "downloadFailed": "下载失败", "installing": "安装中...", "installComplete": "安装完成", "installFailed": "安装失败", "updateFailed": "更新失败", "updateCancelled": "更新已取消", "forceUpdate": "强制更新", "forceUpdateMessage": "此更新包含重要的安全修复，必须立即更新", "releaseNotes": "更新日志", "fileValidationFailed": "文件校验失败", "insufficientStorage": "存储空间不足", "networkUnavailable": "网络连接不可用", "wifiRequired": "需要WiFi连接", "permissionRequired": "需要安装权限", "versionInfo": "版本信息"}
/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      update_config.dart
///
/// DESCRIPTION :    更新配置数据模型，包含更新服务的各种配置参数
///
/// AUTHOR :         wei
///
/// HISTORY :        04/08/2025 create

/// UpdateConfig
///
/// PURPOSE:
///     存储应用更新功能的配置参数，包括服务器设置、检查间隔、下载策略等
///
/// FEATURES:
///     - 服务器配置：API地址、超时设置、重试策略
///     - 检查策略：自动检查间隔、触发条件
///     - 下载策略：自动下载、网络限制、存储路径
///     - 用户体验：提醒间隔、强制更新处理
///     - 完整的JSON序列化支持
///     - 配置验证和默认值处理
///
/// USAGE:
///     UpdateConfig config = UpdateConfig.defaultConfig();
///     config = config.copyWith(serverUrl: 'https://api.example.com');
///     bool isValid = config.isValid;
class UpdateConfig {
  /// 更新服务器API地址
  final String serverUrl;
  
  /// 自动检查更新的间隔时间
  final Duration checkInterval;
  
  /// 是否启用自动下载
  final bool autoDownload;
  
  /// 是否仅在WiFi环境下下载
  final bool wifiOnly;
  
  /// 下载失败时的最大重试次数
  final int maxRetries;
  
  /// 网络请求超时时间
  final Duration timeout;
  
  /// 用户跳过更新后的提醒间隔（小时）
  final int skipRemindHours;
  
  /// 下载文件的存储目录路径
  final String? downloadDirectory;
  
  /// 是否启用自动检查更新
  final bool enableAutoCheck;
  
  /// VPN连接时是否检查更新
  final bool checkOnConnect;
  
  /// 应用启动时是否检查更新
  final bool checkOnStartup;
  
  /// 启动检查的延迟时间（秒）
  final int startupCheckDelay;
  
  /// 是否启用更新通知
  final bool enableNotifications;
  
  /// 下载时的并发连接数
  final int downloadConcurrency;
  
  /// 下载缓冲区大小（字节）
  final int downloadBufferSize;

  /// UpdateConfig构造函数
  ///
  /// DESCRIPTION:
  ///     创建更新配置实例
  ///
  /// PARAMETERS:
  ///     serverUrl - 更新服务器API地址
  ///     checkInterval - 自动检查更新的间隔时间
  ///     autoDownload - 是否启用自动下载
  ///     wifiOnly - 是否仅在WiFi环境下下载
  ///     maxRetries - 下载失败时的最大重试次数
  ///     timeout - 网络请求超时时间
  ///     skipRemindHours - 用户跳过更新后的提醒间隔
  ///     downloadDirectory - 下载文件的存储目录路径
  ///     enableAutoCheck - 是否启用自动检查更新
  ///     checkOnConnect - VPN连接时是否检查更新
  ///     checkOnStartup - 应用启动时是否检查更新
  ///     startupCheckDelay - 启动检查的延迟时间
  ///     enableNotifications - 是否启用更新通知
  ///     downloadConcurrency - 下载时的并发连接数
  ///     downloadBufferSize - 下载缓冲区大小
  const UpdateConfig({
    required this.serverUrl,
    this.checkInterval = const Duration(hours: 1),
    this.autoDownload = true,
    this.wifiOnly = true,
    this.maxRetries = 3,
    this.timeout = const Duration(minutes: 5),
    this.skipRemindHours = 1,
    this.downloadDirectory,
    this.enableAutoCheck = true,
    this.checkOnConnect = true,
    this.checkOnStartup = true,
    this.startupCheckDelay = 5,
    this.enableNotifications = true,
    this.downloadConcurrency = 1,
    this.downloadBufferSize = 8192,
  });

  /// copyWith
  ///
  /// DESCRIPTION:
  ///     创建当前实例的副本，可选择性地更新某些字段
  ///
  /// PARAMETERS:
  ///     serverUrl - 更新服务器API地址
  ///     checkInterval - 自动检查更新的间隔时间
  ///     autoDownload - 是否启用自动下载
  ///     wifiOnly - 是否仅在WiFi环境下下载
  ///     maxRetries - 下载失败时的最大重试次数
  ///     timeout - 网络请求超时时间
  ///     skipRemindHours - 用户跳过更新后的提醒间隔
  ///     downloadDirectory - 下载文件的存储目录路径
  ///     enableAutoCheck - 是否启用自动检查更新
  ///     checkOnConnect - VPN连接时是否检查更新
  ///     checkOnStartup - 应用启动时是否检查更新
  ///     startupCheckDelay - 启动检查的延迟时间
  ///     enableNotifications - 是否启用更新通知
  ///     downloadConcurrency - 下载时的并发连接数
  ///     downloadBufferSize - 下载缓冲区大小
  ///
  /// RETURNS:
  ///     UpdateConfig - 新的实例副本
  UpdateConfig copyWith({
    String? serverUrl,
    Duration? checkInterval,
    bool? autoDownload,
    bool? wifiOnly,
    int? maxRetries,
    Duration? timeout,
    int? skipRemindHours,
    String? downloadDirectory,
    bool? enableAutoCheck,
    bool? checkOnConnect,
    bool? checkOnStartup,
    int? startupCheckDelay,
    bool? enableNotifications,
    int? downloadConcurrency,
    int? downloadBufferSize,
  }) {
    return UpdateConfig(
      serverUrl: serverUrl ?? this.serverUrl,
      checkInterval: checkInterval ?? this.checkInterval,
      autoDownload: autoDownload ?? this.autoDownload,
      wifiOnly: wifiOnly ?? this.wifiOnly,
      maxRetries: maxRetries ?? this.maxRetries,
      timeout: timeout ?? this.timeout,
      skipRemindHours: skipRemindHours ?? this.skipRemindHours,
      downloadDirectory: downloadDirectory ?? this.downloadDirectory,
      enableAutoCheck: enableAutoCheck ?? this.enableAutoCheck,
      checkOnConnect: checkOnConnect ?? this.checkOnConnect,
      checkOnStartup: checkOnStartup ?? this.checkOnStartup,
      startupCheckDelay: startupCheckDelay ?? this.startupCheckDelay,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      downloadConcurrency: downloadConcurrency ?? this.downloadConcurrency,
      downloadBufferSize: downloadBufferSize ?? this.downloadBufferSize,
    );
  }

  /// isValid
  ///
  /// DESCRIPTION:
  ///     验证配置的有效性
  ///
  /// RETURNS:
  ///     bool - true表示配置有效，false表示配置无效
  bool get isValid {
    // 检查服务器URL
    if (serverUrl.isEmpty) {
      return false;
    }
    
    try {
      final uri = Uri.parse(serverUrl);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
        return false;
      }
    } catch (e) {
      return false;
    }
    
    // 检查数值范围
    if (maxRetries < 0 || maxRetries > 10) {
      return false;
    }
    
    if (skipRemindHours < 0 || skipRemindHours > 168) { // 最多一周
      return false;
    }
    
    if (startupCheckDelay < 0 || startupCheckDelay > 300) { // 最多5分钟
      return false;
    }
    
    if (downloadConcurrency < 1 || downloadConcurrency > 10) {
      return false;
    }
    
    if (downloadBufferSize < 1024 || downloadBufferSize > 1048576) { // 1KB - 1MB
      return false;
    }
    
    return true;
  }

  /// toJson
  ///
  /// DESCRIPTION:
  ///     将实例转换为JSON Map
  ///
  /// RETURNS:
  ///     Map<String, dynamic> - JSON Map表示
  Map<String, dynamic> toJson() {
    return {
      'serverUrl': serverUrl,
      'checkInterval': checkInterval.inMilliseconds,
      'autoDownload': autoDownload,
      'wifiOnly': wifiOnly,
      'maxRetries': maxRetries,
      'timeout': timeout.inMilliseconds,
      'skipRemindHours': skipRemindHours,
      'downloadDirectory': downloadDirectory,
      'enableAutoCheck': enableAutoCheck,
      'checkOnConnect': checkOnConnect,
      'checkOnStartup': checkOnStartup,
      'startupCheckDelay': startupCheckDelay,
      'enableNotifications': enableNotifications,
      'downloadConcurrency': downloadConcurrency,
      'downloadBufferSize': downloadBufferSize,
    };
  }

  /// fromJson
  ///
  /// DESCRIPTION:
  ///     从JSON Map创建实例
  ///
  /// PARAMETERS:
  ///     json - JSON Map数据
  ///
  /// RETURNS:
  ///     UpdateConfig - 创建的实例
  factory UpdateConfig.fromJson(Map<String, dynamic> json) {
    return UpdateConfig(
      serverUrl: json['serverUrl'] ?? '',
      checkInterval: Duration(milliseconds: json['checkInterval'] ?? 3600000), // 1小时
      autoDownload: json['autoDownload'] ?? true,
      wifiOnly: json['wifiOnly'] ?? true,
      maxRetries: json['maxRetries'] ?? 3,
      timeout: Duration(milliseconds: json['timeout'] ?? 300000), // 5分钟
      skipRemindHours: json['skipRemindHours'] ?? 1,
      downloadDirectory: json['downloadDirectory'],
      enableAutoCheck: json['enableAutoCheck'] ?? true,
      checkOnConnect: json['checkOnConnect'] ?? true,
      checkOnStartup: json['checkOnStartup'] ?? true,
      startupCheckDelay: json['startupCheckDelay'] ?? 5,
      enableNotifications: json['enableNotifications'] ?? true,
      downloadConcurrency: json['downloadConcurrency'] ?? 1,
      downloadBufferSize: json['downloadBufferSize'] ?? 8192,
    );
  }

  /// defaultConfig
  ///
  /// DESCRIPTION:
  ///     创建默认配置实例
  ///
  /// PARAMETERS:
  ///     serverUrl - 服务器URL，如果不提供则使用默认更新服务器
  ///
  /// RETURNS:
  ///     UpdateConfig - 默认配置实例
  factory UpdateConfig.defaultConfig({String? serverUrl}) {
    return UpdateConfig(
      serverUrl: serverUrl ?? 'https://uctest.unisase.cn:9000',
      checkInterval: const Duration(hours: 1),
      autoDownload: true,
      wifiOnly: false, // 允许移动网络下载，提高成功率
      maxRetries: 3,
      timeout: const Duration(minutes: 10), // 增加超时时间到10分钟
      skipRemindHours: 1,
      enableAutoCheck: true,
      checkOnConnect: true, // 启用VPN连接时检查
      checkOnStartup: true,
      startupCheckDelay: 5,
      enableNotifications: true,
      downloadConcurrency: 1,
      downloadBufferSize: 8192,
    );
  }

  @override
  String toString() {
    return 'UpdateConfig(serverUrl: $serverUrl, checkInterval: $checkInterval, '
           'autoDownload: $autoDownload, wifiOnly: $wifiOnly)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UpdateConfig &&
           other.serverUrl == serverUrl &&
           other.checkInterval == checkInterval &&
           other.autoDownload == autoDownload &&
           other.wifiOnly == wifiOnly &&
           other.maxRetries == maxRetries &&
           other.timeout == timeout &&
           other.skipRemindHours == skipRemindHours &&
           other.downloadDirectory == downloadDirectory &&
           other.enableAutoCheck == enableAutoCheck &&
           other.checkOnConnect == checkOnConnect &&
           other.checkOnStartup == checkOnStartup &&
           other.startupCheckDelay == startupCheckDelay &&
           other.enableNotifications == enableNotifications &&
           other.downloadConcurrency == downloadConcurrency &&
           other.downloadBufferSize == downloadBufferSize;
  }

  @override
  int get hashCode {
    return Object.hash(
      serverUrl,
      checkInterval,
      autoDownload,
      wifiOnly,
      maxRetries,
      timeout,
      skipRemindHours,
      downloadDirectory,
      enableAutoCheck,
      checkOnConnect,
      checkOnStartup,
      startupCheckDelay,
      enableNotifications,
      downloadConcurrency,
      downloadBufferSize,
    );
  }
}

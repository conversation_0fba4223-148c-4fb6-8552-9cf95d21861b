/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      update_info.dart
///
/// DESCRIPTION :    更新信息数据模型，包含版本、下载链接、哈希值等更新相关信息
///
/// AUTHOR :         wei
///
/// HISTORY :        04/08/2025 create

import 'update_status.dart';

/// UpdateInfo
///
/// PURPOSE:
///     存储应用更新的完整信息，包括服务端返回的更新数据和本地状态
///
/// FEATURES:
///     - 服务端更新信息：版本号、下载链接、哈希值、更新日志等
///     - 本地状态信息：下载状态、本地文件路径、检查时间等
///     - 用户行为记录：跳过次数、提醒设置等
///     - 完整的JSON序列化支持
///     - 数据验证和完整性检查
///
/// USAGE:
///     UpdateInfo info = UpdateInfo.fromJson(serverResponse);
///     bool hasUpdate = info.updateAvailable;
///     String version = info.version ?? 'Unknown';
class UpdateInfo {
  /// 是否有更新可用
  final bool updateAvailable;
  
  /// 新版本号
  final String? version;
  
  /// 下载链接
  final String? downloadUrl;
  
  /// 文件哈希值
  final String? hash;
  
  /// 哈希算法类型（如 SHA-256）
  final String? hashType;
  
  /// 多语言更新日志
  final Map<String, String>? releaseNotes;
  
  /// 是否强制更新
  final bool forceUpdate;
  
  /// 当前更新状态
  final UpdateStatus status;
  
  /// 本地文件路径
  final String? localFilePath;
  
  /// 最后检查时间
  final DateTime? lastCheckTime;
  
  /// 用户跳过次数
  final int skipCount;
  
  /// 下载进度（0.0 - 1.0）
  final double downloadProgress;
  
  /// 文件大小（字节）
  final int? fileSize;
  
  /// 错误信息
  final String? errorMessage;

  /// UpdateInfo构造函数
  ///
  /// DESCRIPTION:
  ///     创建更新信息实例
  ///
  /// PARAMETERS:
  ///     updateAvailable - 是否有更新可用
  ///     version - 新版本号
  ///     downloadUrl - 下载链接
  ///     hash - 文件哈希值
  ///     hashType - 哈希算法类型
  ///     releaseNotes - 多语言更新日志
  ///     forceUpdate - 是否强制更新
  ///     status - 当前更新状态
  ///     localFilePath - 本地文件路径
  ///     lastCheckTime - 最后检查时间
  ///     skipCount - 用户跳过次数
  ///     downloadProgress - 下载进度
  ///     fileSize - 文件大小
  ///     errorMessage - 错误信息
  const UpdateInfo({
    required this.updateAvailable,
    this.version,
    this.downloadUrl,
    this.hash,
    this.hashType,
    this.releaseNotes,
    this.forceUpdate = false,
    this.status = UpdateStatus.none,
    this.localFilePath,
    this.lastCheckTime,
    this.skipCount = 0,
    this.downloadProgress = 0.0,
    this.fileSize,
    this.errorMessage,
  });

  /// copyWith
  ///
  /// DESCRIPTION:
  ///     创建当前实例的副本，可选择性地更新某些字段
  ///
  /// PARAMETERS:
  ///     updateAvailable - 是否有更新可用
  ///     version - 新版本号
  ///     downloadUrl - 下载链接
  ///     hash - 文件哈希值
  ///     hashType - 哈希算法类型
  ///     releaseNotes - 多语言更新日志
  ///     forceUpdate - 是否强制更新
  ///     status - 当前更新状态
  ///     localFilePath - 本地文件路径
  ///     lastCheckTime - 最后检查时间
  ///     skipCount - 用户跳过次数
  ///     downloadProgress - 下载进度
  ///     fileSize - 文件大小
  ///     errorMessage - 错误信息
  ///
  /// RETURNS:
  ///     UpdateInfo - 新的实例副本
  UpdateInfo copyWith({
    bool? updateAvailable,
    String? version,
    String? downloadUrl,
    String? hash,
    String? hashType,
    Map<String, String>? releaseNotes,
    bool? forceUpdate,
    UpdateStatus? status,
    String? localFilePath,
    DateTime? lastCheckTime,
    int? skipCount,
    double? downloadProgress,
    int? fileSize,
    String? errorMessage,
  }) {
    return UpdateInfo(
      updateAvailable: updateAvailable ?? this.updateAvailable,
      version: version ?? this.version,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      hash: hash ?? this.hash,
      hashType: hashType ?? this.hashType,
      releaseNotes: releaseNotes ?? this.releaseNotes,
      forceUpdate: forceUpdate ?? this.forceUpdate,
      status: status ?? this.status,
      localFilePath: localFilePath ?? this.localFilePath,
      lastCheckTime: lastCheckTime ?? this.lastCheckTime,
      skipCount: skipCount ?? this.skipCount,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      fileSize: fileSize ?? this.fileSize,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// isValid
  ///
  /// DESCRIPTION:
  ///     验证更新信息的有效性
  ///
  /// RETURNS:
  ///     bool - true表示数据有效，false表示数据无效
  bool get isValid {
    if (!updateAvailable) {
      return true; // 无更新时总是有效
    }
    
    // 有更新时需要验证必要字段
    return version != null &&
           version!.isNotEmpty &&
           downloadUrl != null &&
           downloadUrl!.isNotEmpty &&
           hash != null &&
           hash!.isNotEmpty &&
           hashType != null &&
           hashType!.isNotEmpty;
  }

  /// canDownload
  ///
  /// DESCRIPTION:
  ///     检查是否可以开始下载
  ///
  /// RETURNS:
  ///     bool - true表示可以下载，false表示不可以下载
  bool get canDownload {
    return updateAvailable &&
           isValid &&
           (status == UpdateStatus.available || status == UpdateStatus.failed);
  }

  /// canInstall
  ///
  /// DESCRIPTION:
  ///     检查是否可以开始安装
  ///
  /// RETURNS:
  ///     bool - true表示可以安装，false表示不可以安装
  bool get canInstall {
    return updateAvailable &&
           status == UpdateStatus.downloaded &&
           localFilePath != null &&
           localFilePath!.isNotEmpty;
  }

  /// getReleaseNotes
  ///
  /// DESCRIPTION:
  ///     获取指定语言的更新日志
  ///
  /// PARAMETERS:
  ///     languageCode - 语言代码（如 'zh', 'en'）
  ///
  /// RETURNS:
  ///     String - 更新日志文本，如果没有则返回空字符串
  String getReleaseNotes(String languageCode) {
    if (releaseNotes == null) {
      return '';
    }
    
    // 优先返回指定语言的日志
    if (releaseNotes!.containsKey(languageCode)) {
      return releaseNotes![languageCode] ?? '';
    }
    
    // 回退到英文
    if (releaseNotes!.containsKey('en')) {
      return releaseNotes!['en'] ?? '';
    }
    
    // 回退到中文
    if (releaseNotes!.containsKey('zh')) {
      return releaseNotes!['zh'] ?? '';
    }
    
    // 返回第一个可用的语言
    if (releaseNotes!.isNotEmpty) {
      return releaseNotes!.values.first;
    }
    
    return '';
  }

  /// toJson
  ///
  /// DESCRIPTION:
  ///     将实例转换为JSON Map
  ///
  /// RETURNS:
  ///     Map<String, dynamic> - JSON Map表示
  Map<String, dynamic> toJson() {
    return {
      'updateAvailable': updateAvailable,
      'version': version,
      'downloadUrl': downloadUrl,
      'hash': hash,
      'hashType': hashType,
      'releaseNotes': releaseNotes,
      'forceUpdate': forceUpdate,
      'status': status.toJson(),
      'localFilePath': localFilePath,
      'lastCheckTime': lastCheckTime?.toIso8601String(),
      'skipCount': skipCount,
      'downloadProgress': downloadProgress,
      'fileSize': fileSize,
      'errorMessage': errorMessage,
    };
  }

  /// fromJson
  ///
  /// DESCRIPTION:
  ///     从JSON Map创建实例
  ///
  /// PARAMETERS:
  ///     json - JSON Map数据
  ///
  /// RETURNS:
  ///     UpdateInfo - 创建的实例
  factory UpdateInfo.fromJson(Map<String, dynamic> json) {
    return UpdateInfo(
      updateAvailable: json['updateAvailable'] ?? false,
      version: json['version'],
      downloadUrl: json['downloadUrl'],
      hash: json['hash'],
      hashType: json['hashType'],
      releaseNotes: json['releaseNotes'] != null
          ? Map<String, String>.from(json['releaseNotes'])
          : null,
      forceUpdate: json['forceUpdate'] ?? false,
      status: json['status'] != null
          ? UpdateStatus.fromJson(json['status'])
          : UpdateStatus.none,
      localFilePath: json['localFilePath'],
      lastCheckTime: json['lastCheckTime'] != null
          ? DateTime.parse(json['lastCheckTime'])
          : null,
      skipCount: json['skipCount'] ?? 0,
      downloadProgress: (json['downloadProgress'] ?? 0.0).toDouble(),
      fileSize: json['fileSize'],
      errorMessage: json['errorMessage'],
    );
  }

  /// noUpdate
  ///
  /// DESCRIPTION:
  ///     创建一个表示无更新的实例
  ///
  /// RETURNS:
  ///     UpdateInfo - 无更新的实例
  factory UpdateInfo.noUpdate() {
    return const UpdateInfo(
      updateAvailable: false,
      status: UpdateStatus.none,
    );
  }

  @override
  String toString() {
    return 'UpdateInfo(updateAvailable: $updateAvailable, version: $version, '
           'status: $status, downloadProgress: $downloadProgress)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UpdateInfo &&
           other.updateAvailable == updateAvailable &&
           other.version == version &&
           other.downloadUrl == downloadUrl &&
           other.hash == hash &&
           other.hashType == hashType &&
           other.forceUpdate == forceUpdate &&
           other.status == status &&
           other.localFilePath == localFilePath &&
           other.skipCount == skipCount &&
           other.downloadProgress == downloadProgress &&
           other.fileSize == fileSize &&
           other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return Object.hash(
      updateAvailable,
      version,
      downloadUrl,
      hash,
      hashType,
      forceUpdate,
      status,
      localFilePath,
      skipCount,
      downloadProgress,
      fileSize,
      errorMessage,
    );
  }
}

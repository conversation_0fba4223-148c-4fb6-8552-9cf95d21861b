/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      update_status.dart
///
/// DESCRIPTION :    更新状态枚举定义，用于表示应用更新的各种状态
///
/// AUTHOR :         wei
///
/// HISTORY :        04/08/2025 create

/// UpdateStatus
///
/// PURPOSE:
///     定义应用更新过程中的各种状态，用于状态管理和UI显示
///
/// FEATURES:
///     - 完整的更新生命周期状态覆盖
///     - 支持状态转换验证
///     - 提供状态描述和显示名称
///     - 支持JSON序列化
///
/// USAGE:
///     UpdateStatus status = UpdateStatus.downloading;
///     String displayName = status.displayName;
///     bool canTransition = status.canTransitionTo(UpdateStatus.downloaded);
enum UpdateStatus {
  /// 无更新可用
  none,
  
  /// 检查更新中
  checking,
  
  /// 有更新可用
  available,
  
  /// 下载中
  downloading,
  
  /// 下载完成
  downloaded,
  
  /// 安装中
  installing,
  
  /// 安装完成
  installed,
  
  /// 更新失败
  failed,
  
  /// 用户跳过更新
  skipped,
  
  /// 更新被取消
  cancelled;

  /// displayName
  ///
  /// DESCRIPTION:
  ///     获取状态的显示名称，用于UI展示
  ///
  /// RETURNS:
  ///     String - 状态的显示名称
  String get displayName {
    switch (this) {
      case UpdateStatus.none:
        return '无更新';
      case UpdateStatus.checking:
        return '检查中';
      case UpdateStatus.available:
        return '可更新';
      case UpdateStatus.downloading:
        return '下载中';
      case UpdateStatus.downloaded:
        return '下载完成';
      case UpdateStatus.installing:
        return '安装中';
      case UpdateStatus.installed:
        return '安装完成';
      case UpdateStatus.failed:
        return '更新失败';
      case UpdateStatus.skipped:
        return '已跳过';
      case UpdateStatus.cancelled:
        return '已取消';
    }
  }

  /// description
  ///
  /// DESCRIPTION:
  ///     获取状态的详细描述
  ///
  /// RETURNS:
  ///     String - 状态的详细描述
  String get description {
    switch (this) {
      case UpdateStatus.none:
        return '当前已是最新版本';
      case UpdateStatus.checking:
        return '正在检查是否有新版本...';
      case UpdateStatus.available:
        return '发现新版本，可以更新';
      case UpdateStatus.downloading:
        return '正在下载更新包...';
      case UpdateStatus.downloaded:
        return '更新包下载完成，准备安装';
      case UpdateStatus.installing:
        return '正在安装更新...';
      case UpdateStatus.installed:
        return '更新安装完成';
      case UpdateStatus.failed:
        return '更新过程中发生错误';
      case UpdateStatus.skipped:
        return '用户选择跳过此次更新';
      case UpdateStatus.cancelled:
        return '更新操作被用户取消';
    }
  }

  /// isInProgress
  ///
  /// DESCRIPTION:
  ///     判断当前状态是否为进行中状态
  ///
  /// RETURNS:
  ///     bool - true表示正在进行中，false表示已完成或未开始
  bool get isInProgress {
    return this == UpdateStatus.checking ||
           this == UpdateStatus.downloading ||
           this == UpdateStatus.installing;
  }

  /// isCompleted
  ///
  /// DESCRIPTION:
  ///     判断当前状态是否为完成状态（成功或失败）
  ///
  /// RETURNS:
  ///     bool - true表示已完成，false表示未完成
  bool get isCompleted {
    return this == UpdateStatus.installed ||
           this == UpdateStatus.failed ||
           this == UpdateStatus.skipped ||
           this == UpdateStatus.cancelled;
  }

  /// isError
  ///
  /// DESCRIPTION:
  ///     判断当前状态是否为错误状态
  ///
  /// RETURNS:
  ///     bool - true表示错误状态，false表示正常状态
  bool get isError {
    return this == UpdateStatus.failed;
  }

  /// canTransitionTo
  ///
  /// DESCRIPTION:
  ///     检查是否可以从当前状态转换到目标状态
  ///
  /// PARAMETERS:
  ///     target - 目标状态
  ///
  /// RETURNS:
  ///     bool - true表示可以转换，false表示不可以转换
  bool canTransitionTo(UpdateStatus target) {
    switch (this) {
      case UpdateStatus.none:
        return target == UpdateStatus.checking;
      case UpdateStatus.checking:
        return target == UpdateStatus.none ||
               target == UpdateStatus.available ||
               target == UpdateStatus.failed;
      case UpdateStatus.available:
        return target == UpdateStatus.downloading ||
               target == UpdateStatus.skipped ||
               target == UpdateStatus.cancelled;
      case UpdateStatus.downloading:
        return target == UpdateStatus.downloaded ||
               target == UpdateStatus.failed ||
               target == UpdateStatus.cancelled;
      case UpdateStatus.downloaded:
        return target == UpdateStatus.installing ||
               target == UpdateStatus.failed;
      case UpdateStatus.installing:
        return target == UpdateStatus.installed ||
               target == UpdateStatus.failed;
      case UpdateStatus.installed:
      case UpdateStatus.failed:
      case UpdateStatus.skipped:
      case UpdateStatus.cancelled:
        return target == UpdateStatus.none ||
               target == UpdateStatus.checking;
    }
  }

  /// fromString
  ///
  /// DESCRIPTION:
  ///     从字符串创建UpdateStatus枚举值
  ///
  /// PARAMETERS:
  ///     value - 字符串值
  ///
  /// RETURNS:
  ///     UpdateStatus - 对应的枚举值，如果无法解析则返回none
  static UpdateStatus fromString(String value) {
    for (UpdateStatus status in UpdateStatus.values) {
      if (status.name == value) {
        return status;
      }
    }
    return UpdateStatus.none;
  }

  /// toJson
  ///
  /// DESCRIPTION:
  ///     将枚举值转换为JSON字符串
  ///
  /// RETURNS:
  ///     String - JSON字符串表示
  String toJson() => name;

  /// fromJson
  ///
  /// DESCRIPTION:
  ///     从JSON字符串创建UpdateStatus枚举值
  ///
  /// PARAMETERS:
  ///     json - JSON字符串
  ///
  /// RETURNS:
  ///     UpdateStatus - 对应的枚举值
  static UpdateStatus fromJson(String json) => fromString(json);
}

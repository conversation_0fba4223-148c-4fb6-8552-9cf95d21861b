// /**
//  * AUTHOR: wei
//  * HISTORY: 27/06/2025 - Initial implementation of platform service factory
//  */

import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'cross_platform_api_service.dart';
import 'http_api_service.dart';
import 'platform_channel_api_service.dart';
import 'cross_platform_storage_service.dart';
import 'file_storage_service.dart';
import 'windows_storage_service.dart';
import 'cross_platform_log_service.dart';
import 'file_log_service.dart';
import 'cross_platform_backend_service.dart';
import 'platform_channel_backend_service.dart';
import 'http_backend_service.dart';
import 'platform_update_service.dart';
import 'windows_update_service.dart';
import 'android_update_service.dart';
import 'ios_update_service.dart';
import '../log_service.dart';

/// PlatformServiceFactory
///
/// PURPOSE:
///     Factory class that creates platform-specific service implementations.
///     Automatically detects the current platform and returns appropriate
///     service instances for API communication, storage, and logging.
///
/// FEATURES:
///     - Automatic platform detection (iOS/macOS vs Windows/Linux)
///     - Lazy initialization with singleton pattern
///     - Type-safe service creation
///     - Fallback implementations for unsupported platforms
///     - Resource management and cleanup
///
/// USAGE:
///     final apiService = PlatformServiceFactory.createApiService();
///     final storageService = PlatformServiceFactory.createStorageService();
///     final logService = PlatformServiceFactory.createLogService();
class PlatformServiceFactory {
  
  // ============================================================================
  // SINGLETON INSTANCES
  // ============================================================================

  static CrossPlatformApiService? _apiServiceInstance;
  static CrossPlatformStorageService? _storageServiceInstance;
  static CrossPlatformLogService? _logServiceInstance;
  static CrossPlatformBackendService? _backendServiceInstance;
  static PlatformUpdateService? _updateServiceInstance;

  // ============================================================================
  // PLATFORM DETECTION CACHE
  // ============================================================================

  static bool _isInitialized = false;
  static bool _cachedIsIOSAppOnMacOS = false;

  // ============================================================================
  // PLATFORM DETECTION
  // ============================================================================

  /// platformName
  ///
  /// DESCRIPTION:
  ///     Get simple platform name for display purposes.
  ///
  /// RETURNS:
  ///     String - Platform name
  static String get platformName {
    if (Platform.isIOS) {
      return 'iOS';
    } else if (Platform.isMacOS) {
      return 'macOS';
    } else if (Platform.isAndroid) {
      return 'Android';
    } else if (Platform.isWindows) {
      return 'Windows';
    } else if (Platform.isLinux) {
      return 'Linux';
    } else {
      return 'Unknown';
    }
  }

  /// isApplePlatform
  ///
  /// DESCRIPTION:
  ///     Check if current platform is iOS or macOS.
  ///
  /// RETURNS:
  ///     bool - true if iOS or macOS, false otherwise
  static bool get isApplePlatform => Platform.isIOS || Platform.isMacOS;

  /// isMobilePlatform
  ///
  /// DESCRIPTION:
  ///     Check if current platform is a mobile platform (iOS or Android).
  ///
  /// RETURNS:
  ///     bool - true if mobile platform, false otherwise
  static bool get isMobilePlatform => Platform.isIOS || Platform.isAndroid;

  /// isDesktopPlatform
  ///
  /// DESCRIPTION:
  ///     Check if current platform is a desktop platform (Windows, Linux, macOS).
  ///
  /// RETURNS:
  ///     bool - true if desktop platform, false otherwise
  static bool get isDesktopPlatform => Platform.isWindows || Platform.isLinux || Platform.isMacOS;

  /// initializePlatformDetection
  ///
  /// DESCRIPTION:
  ///     Initialize platform detection and cache results for better performance.
  ///     Should be called once during app initialization.
  ///
  /// RETURNS:
  ///     Future<void> - completion of initialization
  static Future<void> initializePlatformDetection() async {
    if (_isInitialized) return;

    // Cache iOS App on macOS detection result
    if (Platform.isIOS) {
      try {
        final deviceInfo = DeviceInfoPlugin();
        final iosInfo = await deviceInfo.iosInfo;
        _cachedIsIOSAppOnMacOS = iosInfo.isiOSAppOnMac;
      } catch (e) {
        _cachedIsIOSAppOnMacOS = false;
      }
    } else {
      _cachedIsIOSAppOnMacOS = false;
    }

    _isInitialized = true;
  }

  /// shouldUseDesktopLayoutSync
  ///
  /// DESCRIPTION:
  ///     Synchronous method to determine if desktop layout should be used.
  ///     Uses cached platform detection results for better performance.
  ///     Note: Call initializePlatformDetection() first for accurate iOS App on macOS detection.
  ///
  /// RETURNS:
  ///     bool - true if desktop layout should be used
  static bool shouldUseDesktopLayoutSync() {
    if (isDesktopPlatform) return true;

    // Use cached iOS App on macOS detection if initialized
    if (_isInitialized && Platform.isIOS) {
      return _cachedIsIOSAppOnMacOS;
    }

    // Fallback to basic platform detection
    return isDesktopPlatform;
  }

  /// isIOSAppOnMacOS
  ///
  /// DESCRIPTION:
  ///     Check if this is iOS App running on macOS.
  ///     Uses cached result if available, otherwise performs detection.
  ///     When isiOSAppOnMac=true, should be treated as macOS for UI layout.
  ///
  /// RETURNS:
  ///     Future<bool> - true if iOS App running on macOS
  static Future<bool> isIOSAppOnMacOS() async {
    if (!Platform.isIOS) return false;

    // Return cached result if available
    if (_isInitialized) {
      return _cachedIsIOSAppOnMacOS;
    }

    // Perform detection if not initialized
    try {
      final deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.isiOSAppOnMac;
    } catch (e) {
      return false;
    }
  }

  /// shouldUseDesktopLayout
  ///
  /// DESCRIPTION:
  ///     Async method to determine if desktop layout should be used.
  ///     Considers both platform and iOS App on macOS detection.
  ///     When isiOSAppOnMac=true, should use desktop layout with sidebar.
  ///
  /// RETURNS:
  ///     Future<bool> - true if desktop layout should be used
  static Future<bool> shouldUseDesktopLayout() async {
    if (isDesktopPlatform) return true;

    // Check if iOS App running on macOS
    if (Platform.isIOS) {
      return await isIOSAppOnMacOS();
    }

    return false;
  }


  
  // ============================================================================
  // API SERVICE FACTORY
  // ============================================================================
  
  /// createApiService
  ///
  /// DESCRIPTION:
  ///     Create platform-specific API service implementation.
  ///     Returns singleton instance for consistent state management.
  ///
  /// RETURNS:
  ///     CrossPlatformApiService - platform-specific API service
  ///
  /// THROWS:
  ///     UnsupportedError - if platform is not supported
  static CrossPlatformApiService createApiService() {
    if (_apiServiceInstance != null) {
      return _apiServiceInstance!;
    }
    
    try {
      if (isApplePlatform || Platform.isAndroid) {
        _apiServiceInstance = PlatformChannelApiService();
      } else if (isDesktopPlatform) {
        _apiServiceInstance = HttpApiService();
      } else {
        throw UnsupportedError('Platform ${Platform.operatingSystem} not supported');
      }
      
      return _apiServiceInstance!;
    } catch (e) {
      // Log error and provide fallback
      // Note: Using print here as this is factory initialization, logging service may not be available yet
      // In production, this will be handled by the logging framework
      // debugPrint('Failed to create API service for $platformName: $e');
      throw UnsupportedError('Cannot create API service for $platformName: $e');
    }
  }
  
  // ============================================================================
  // STORAGE SERVICE FACTORY
  // ============================================================================
  
  /// createStorageService
  ///
  /// DESCRIPTION:
  ///     Create platform-specific storage service implementation.
  ///     iOS/macOS: UserDefaults + Keychain + App Group storage
  ///     Windows/Linux: File-based storage with encryption
  ///
  /// RETURNS:
  ///     CrossPlatformStorageService - platform-specific storage service
  static CrossPlatformStorageService createStorageService() {
    if (_storageServiceInstance != null) {
      return _storageServiceInstance!;
    }
    
    try {
      if (isApplePlatform) {
        // iOS/macOS临时使用 SharedPreferences 实现，避免 Platform Channel 依赖
        _storageServiceInstance = FileStorageService();
      } else if (Platform.isAndroid) {
        // Android使用统一的FileStorageService，支持Android路径
        _storageServiceInstance = FileStorageService();
      } else if (Platform.isWindows) {
        _storageServiceInstance = WindowsStorageService();
      } else if (isDesktopPlatform) {
        _storageServiceInstance = FileStorageService();
      } else {
        throw UnsupportedError('Platform ${Platform.operatingSystem} not supported');
      }
      
      return _storageServiceInstance!;
    } catch (e) {
      // debugPrint('Failed to create storage service for $platformName: $e');
      throw UnsupportedError('Cannot create storage service for $platformName: $e');
    }
  }
  
  // ============================================================================
  // LOG SERVICE FACTORY
  // ============================================================================
  
  /// createLogService
  ///
  /// DESCRIPTION:
  ///     Create platform-specific log service implementation.
  ///     All platforms: File-based logging with rotation for simplicity
  ///     (Swift backend uses OSLog independently)
  ///
  /// RETURNS:
  ///     CrossPlatformLogService - platform-specific log service
  static CrossPlatformLogService createLogService() {
    if (_logServiceInstance != null) {
      return _logServiceInstance!;
    }

    try {
      // Use FileLogService for all platforms to simplify architecture
      // iOS/macOS Swift backend uses OSLog independently
      // Android uses enhanced FileLogService with LoggingManager integration
      _logServiceInstance = FileLogService();

      return _logServiceInstance!;
    } catch (e) {
      // debugPrint('Failed to create log service for $platformName: $e');
      throw UnsupportedError('Cannot create log service for $platformName: $e');
    }
  }
  
  // ============================================================================
  // BACKEND SERVICE FACTORY
  // ============================================================================

  /// createBackendService
  ///
  /// DESCRIPTION:
  ///     Create platform-specific backend service implementation.
  ///     iOS/macOS: Platform Channel communication with Swift backend
  ///     Windows/Linux: HTTP communication with Go backend process
  ///
  /// RETURNS:
  ///     CrossPlatformBackendService - platform-specific backend service
  static CrossPlatformBackendService createBackendService() {
    if (_backendServiceInstance != null) {
      return _backendServiceInstance!;
    }

    try {
      if (isApplePlatform || Platform.isAndroid) {
        _backendServiceInstance = PlatformChannelBackendService();
      } else if (isDesktopPlatform) {
        _backendServiceInstance = HttpBackendService(logService: createLegacyLogService());
      } else {
        throw UnsupportedError('Platform ${Platform.operatingSystem} not supported');
      }

      return _backendServiceInstance!;
    } catch (e) {
      // debugPrint('Failed to create backend service for $platformName: $e');
      throw UnsupportedError('Cannot create backend service for $platformName: $e');
    }
  }

  // ============================================================================
  // UPDATE SERVICE FACTORY
  // ============================================================================

  /// createUpdateService
  ///
  /// DESCRIPTION:
  ///     Create platform-specific update service implementation.
  ///     Windows: EXE installer execution
  ///     Android: APK installation via intent
  ///     iOS: App Store redirection
  ///
  /// RETURNS:
  ///     PlatformUpdateService - platform-specific update service
  static PlatformUpdateService createUpdateService() {
    if (_updateServiceInstance != null) {
      return _updateServiceInstance!;
    }

    try {
      if (Platform.isWindows) {
        _updateServiceInstance = WindowsUpdateService();
      } else if (Platform.isAndroid) {
        _updateServiceInstance = AndroidUpdateService();
      } else if (Platform.isIOS) {
        _updateServiceInstance = IOSUpdateService();
      } else {
        throw UnsupportedError('Platform ${Platform.operatingSystem} not supported for updates');
      }

      return _updateServiceInstance!;
    } catch (e) {
      throw UnsupportedError('Cannot create update service for $platformName: $e');
    }
  }

  // ============================================================================
  // LEGACY COMPATIBILITY
  // ============================================================================

  /// createLegacyLogService
  ///
  /// DESCRIPTION:
  ///     Create legacy LogService instance for backward compatibility.
  ///     Wraps the new CrossPlatformLogService in legacy interface.
  ///
  /// RETURNS:
  ///     LogService - legacy log service wrapper
  static LogService createLegacyLogService() {
    // Ensure cross-platform log service is initialized
    createLogService();
    return LogService(); // Will be adapted to use crossPlatformLogService internally
  }
  
  // ============================================================================
  // RESOURCE MANAGEMENT
  // ============================================================================
  
  /// disposeAll
  ///
  /// DESCRIPTION:
  ///     Dispose all created service instances and clean up resources.
  ///     Should be called during application shutdown.
  ///
  /// RETURNS:
  ///     Future<void> - completion of cleanup operations
  static Future<void> disposeAll() async {
    final List<Future<void>> disposeFutures = [];

    if (_apiServiceInstance != null) {
      disposeFutures.add(_apiServiceInstance!.dispose());
      _apiServiceInstance = null;
    }

    if (_storageServiceInstance != null) {
      disposeFutures.add(_storageServiceInstance!.dispose());
      _storageServiceInstance = null;
    }

    if (_logServiceInstance != null) {
      disposeFutures.add(_logServiceInstance!.dispose());
      _logServiceInstance = null;
    }

    if (_backendServiceInstance != null) {
      disposeFutures.add(_backendServiceInstance!.dispose());
      _backendServiceInstance = null;
    }

    if (_updateServiceInstance != null) {
      disposeFutures.add(_updateServiceInstance!.dispose());
      _updateServiceInstance = null;
    }

    await Future.wait(disposeFutures);
  }
  
  /// resetInstances
  ///
  /// DESCRIPTION:
  ///     Reset all singleton instances without disposing.
  ///     Useful for testing or when switching configurations.
  ///
  /// RETURNS:
  ///     void
  static void resetInstances() {
    _apiServiceInstance = null;
    _storageServiceInstance = null;
    _logServiceInstance = null;
    _backendServiceInstance = null;
    _updateServiceInstance = null;
  }
  
  // ============================================================================
  // PLATFORM CAPABILITIES
  // ============================================================================
  
  /// getPlatformCapabilities
  ///
  /// DESCRIPTION:
  ///     Get platform-specific capabilities and limitations.
  ///     Note: This method doesn't require BuildContext, so it returns
  ///     capabilities based on the actual platform, not runtime behavior.
  ///     For iOS App on macOS detection, use shouldUseDesktopLayoutSync() for UI-related capabilities.
  ///
  /// RETURNS:
  ///     Map<String, dynamic> - platform capabilities information
  static Map<String, dynamic> getPlatformCapabilities() {
    final shouldUseDesktop = shouldUseDesktopLayoutSync();

    return {
      'platform': platformName,
      'isApplePlatform': isApplePlatform,
      'isMobilePlatform': isMobilePlatform,
      'isDesktopPlatform': isDesktopPlatform,
      'supportsBackgroundExecution': isApplePlatform || Platform.isAndroid,
      'supportsWindowManagement': shouldUseDesktop,
      'supportsOrientationChange': (Platform.isIOS && !shouldUseDesktop) || Platform.isAndroid,
      'requiresVPNPermission': isApplePlatform || Platform.isAndroid,
      'supportsPlatformChannels': isApplePlatform || Platform.isAndroid,
      'supportsHttpApi': isDesktopPlatform,
      'supportsFileStorage': true,
      'supportsSecureStorage': true,
      'supportsAppGroupStorage': isApplePlatform,
      'supportsAutoUpdate': true,
      'supportsAppStoreUpdate': Platform.isIOS,
      'supportsDirectInstall': Platform.isWindows || Platform.isAndroid,
      'requiresInstallPermission': Platform.isAndroid,
    };
  }




}

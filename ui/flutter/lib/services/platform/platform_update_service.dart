/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      platform_update_service.dart
///
/// DESCRIPTION :    平台特定更新服务的抽象接口定义
///
/// AUTHOR :         wei
///
/// HISTORY :        04/08/2025 create

import 'dart:io';

/// PlatformUpdateService
///
/// PURPOSE:
///     定义平台特定更新服务的抽象接口，各平台需要实现具体的更新逻辑
///
/// FEATURES:
///     - 平台特定的安装逻辑：Windows EXE、Android APK、iOS App Store
///     - 文件验证和完整性检查
///     - 权限处理和用户交互
///     - 下载目录管理
///     - 错误处理和状态反馈
///
/// USAGE:
///     PlatformUpdateService service = PlatformServiceFactory.createUpdateService();
///     await service.installUpdate('/path/to/update.exe');
abstract class PlatformUpdateService {
  /// installUpdate
  ///
  /// DESCRIPTION:
  ///     安装更新文件，具体实现因平台而异
  ///
  /// PARAMETERS:
  ///     filePath - 更新文件的本地路径
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 安装失败时抛出异常
  Future<void> installUpdate(String filePath);

  /// openAppStore
  ///
  /// DESCRIPTION:
  ///     打开应用商店进行更新（主要用于iOS平台）
  ///
  /// PARAMETERS:
  ///     appId - 应用在商店中的ID
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 打开失败时抛出异常
  Future<void> openAppStore(String appId);

  /// getDownloadDirectory
  ///
  /// DESCRIPTION:
  ///     获取平台特定的下载目录路径
  ///
  /// RETURNS:
  ///     String - 下载目录的绝对路径
  String getDownloadDirectory();

  /// validateFile
  ///
  /// DESCRIPTION:
  ///     验证下载文件的完整性和正确性
  ///
  /// PARAMETERS:
  ///     filePath - 文件路径
  ///     expectedHash - 期望的哈希值
  ///     hashType - 哈希算法类型（如 'SHA-256'）
  ///
  /// RETURNS:
  ///     Future<bool> - true表示验证通过，false表示验证失败
  Future<bool> validateFile(String filePath, String expectedHash, String hashType);

  /// checkPermissions
  ///
  /// DESCRIPTION:
  ///     检查安装更新所需的权限
  ///
  /// RETURNS:
  ///     Future<bool> - true表示有权限，false表示无权限
  Future<bool> checkPermissions();

  /// requestPermissions
  ///
  /// DESCRIPTION:
  ///     请求安装更新所需的权限
  ///
  /// RETURNS:
  ///     Future<bool> - true表示权限获取成功，false表示失败
  Future<bool> requestPermissions();

  /// canInstallUpdates
  ///
  /// DESCRIPTION:
  ///     检查当前平台是否支持自动安装更新
  ///
  /// RETURNS:
  ///     bool - true表示支持，false表示不支持
  bool canInstallUpdates();

  /// getFileExtension
  ///
  /// DESCRIPTION:
  ///     获取当前平台的更新文件扩展名
  ///
  /// RETURNS:
  ///     String - 文件扩展名（如 '.exe', '.apk'）
  String getFileExtension();

  /// cleanupOldFiles
  ///
  /// DESCRIPTION:
  ///     清理旧的更新文件
  ///
  /// PARAMETERS:
  ///     keepLatest - 保留最新的文件数量
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> cleanupOldFiles({int keepLatest = 1});

  /// getAvailableSpace
  ///
  /// DESCRIPTION:
  ///     获取下载目录的可用空间（字节）
  ///
  /// RETURNS:
  ///     Future<int> - 可用空间大小
  Future<int> getAvailableSpace();

  /// isNetworkAvailable
  ///
  /// DESCRIPTION:
  ///     检查网络连接是否可用
  ///
  /// RETURNS:
  ///     Future<bool> - true表示网络可用，false表示不可用
  Future<bool> isNetworkAvailable();

  /// isWifiConnected
  ///
  /// DESCRIPTION:
  ///     检查是否连接到WiFi网络
  ///
  /// RETURNS:
  ///     Future<bool> - true表示连接WiFi，false表示未连接
  Future<bool> isWifiConnected();

  /// showUpdateNotification
  ///
  /// DESCRIPTION:
  ///     显示更新通知
  ///
  /// PARAMETERS:
  ///     title - 通知标题
  ///     message - 通知内容
  ///     isForceUpdate - 是否为强制更新
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> showUpdateNotification(String title, String message, {bool isForceUpdate = false});

  /// hideUpdateNotification
  ///
  /// DESCRIPTION:
  ///     隐藏更新通知
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> hideUpdateNotification();

  /// getPlatformType
  ///
  /// DESCRIPTION:
  ///     获取当前平台类型
  ///
  /// RETURNS:
  ///     String - 平台类型（'windows', 'android', 'ios'）
  String getPlatformType();

  /// getCurrentVersion
  ///
  /// DESCRIPTION:
  ///     获取当前应用版本号
  ///
  /// RETURNS:
  ///     Future<String> - 当前版本号
  Future<String> getCurrentVersion();

  /// getAppId
  ///
  /// DESCRIPTION:
  ///     获取应用在各平台商店中的ID
  ///
  /// RETURNS:
  ///     String? - 应用ID，如果不适用则返回null
  String? getAppId();

  /// dispose
  ///
  /// DESCRIPTION:
  ///     释放资源和清理
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> dispose();
}

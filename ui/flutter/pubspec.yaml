name: panabit_client
description: Panabit Client with Flutter UI and Go backend.
version: 1.1.0+1
publish_to: 'none'

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6
  http: ^1.1.0
  web_socket_channel: ^2.4.0
  provider: ^6.0.5
  get_it: ^7.6.4
  shared_preferences: ^2.2.1
  path_provider: ^2.1.1
  intl: ^0.20.2
  flutter_svg: ^2.0.7
  window_manager: ^0.3.9
  tray_manager: ^0.5.0
  url_launcher: ^6.2.5
  path: ^1.8.3
  crypto: ^3.0.3
  encrypt: ^5.0.3
  device_info_plus: ^11.5.0
  uuid: ^3.0.7
  flutter_localizations:
    sdk: flutter
  flutter_gen: ^5.3.2
  # 自动更新功能依赖
  package_info_plus: ^8.0.0
  dio: ^5.4.0
  android_intent_plus: ^5.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.3
  build_runner: ^2.4.6
  flutter_launcher_icons: ^0.13.1
  msix: ^3.16.4
  mockito: ^5.4.2

flutter:
  uses-material-design: true
  generate: true
  assets:
    # 实际使用的图片资源 - Panabit品牌图标
    - assets/icons/app_icon.png
    - assets/icons/app_icon.webp
    - assets/icons/app_icon.ico
    - assets/icons/panabit_icon.png
    - assets/icons/panabit_icon.svg
    - assets/icons/panabit_logo.png
    - assets/icons/panabit_logo.svg
    - assets/icons/panabit_r.svg
    # 新设计系统SVG资源 - 实际使用的
    - assets/images/ITFORCE_Logo.svg
    - assets/images/itforce_shield_log.svg
    - assets/images/itforce_letter_logo_light.svg
    - assets/images/connected.svg
    - assets/images/disconnected.svg
    - assets/images/connected_en.svg
    - assets/images/disconnected_en.svg
    - assets/images/mask.svg
    # HTML文档资源
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
    - family: SourceHanSansCN
      fonts:
        - asset: assets/fonts/SourceHanSansCN-Regular.otf
          weight: 400
        - asset: assets/fonts/SourceHanSansCN-Light.otf
          weight: 300
        - asset: assets/fonts/SourceHanSansCN-Medium.otf
          weight: 500
        - asset: assets/fonts/SourceHanSansCN-Bold.otf
          weight: 700

flutter_launcher_icons:
  android: "ic_launcher"
  ios: false
  image_path: "assets/icons/panabit_icon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icons/panabit_icon.png"
  windows:
    generate: true
    image_path: "assets/icons/panabit_icon.webp"
    icon_size: 256

msix_config:
  display_name: Panabit iWAN
  publisher_display_name: Panabit Corporation
  identity_name: Panabit.PanabitClient
  msix_version: 1.1.0.0
  logo_path: assets/icons/panabit_icon.webp
  capabilities: internetClient, runFullTrust
  execution_alias: PanabitClient
  publisher: CN=Panabit Corporation, O=Panabit Corporation, L=City, S=State, C=Country
  store: false

/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      update_models_test.dart
///
/// DESCRIPTION :    更新相关数据模型的单元测试
///
/// AUTHOR :         wei
///
/// HISTORY :        04/08/2025 create

import 'package:flutter_test/flutter_test.dart';
import 'package:panabit_client/models/update_status.dart';
import 'package:panabit_client/models/update_info.dart';
import 'package:panabit_client/models/update_config.dart';

void main() {
  group('UpdateStatus Tests', () {
    test('should have correct display names', () {
      expect(UpdateStatus.none.displayName, '无更新');
      expect(UpdateStatus.checking.displayName, '检查中');
      expect(UpdateStatus.available.displayName, '可更新');
      expect(UpdateStatus.downloading.displayName, '下载中');
      expect(UpdateStatus.downloaded.displayName, '下载完成');
      expect(UpdateStatus.installing.displayName, '安装中');
      expect(UpdateStatus.installed.displayName, '安装完成');
      expect(UpdateStatus.failed.displayName, '更新失败');
      expect(UpdateStatus.skipped.displayName, '已跳过');
      expect(UpdateStatus.cancelled.displayName, '已取消');
    });

    test('should correctly identify progress states', () {
      expect(UpdateStatus.checking.isInProgress, true);
      expect(UpdateStatus.downloading.isInProgress, true);
      expect(UpdateStatus.installing.isInProgress, true);
      expect(UpdateStatus.none.isInProgress, false);
      expect(UpdateStatus.available.isInProgress, false);
      expect(UpdateStatus.downloaded.isInProgress, false);
      expect(UpdateStatus.installed.isInProgress, false);
      expect(UpdateStatus.failed.isInProgress, false);
    });

    test('should correctly identify completed states', () {
      expect(UpdateStatus.installed.isCompleted, true);
      expect(UpdateStatus.failed.isCompleted, true);
      expect(UpdateStatus.skipped.isCompleted, true);
      expect(UpdateStatus.cancelled.isCompleted, true);
      expect(UpdateStatus.none.isCompleted, false);
      expect(UpdateStatus.checking.isCompleted, false);
      expect(UpdateStatus.downloading.isCompleted, false);
    });

    test('should correctly identify error states', () {
      expect(UpdateStatus.failed.isError, true);
      expect(UpdateStatus.none.isError, false);
      expect(UpdateStatus.checking.isError, false);
      expect(UpdateStatus.downloading.isError, false);
      expect(UpdateStatus.installed.isError, false);
    });

    test('should validate state transitions', () {
      // From none
      expect(UpdateStatus.none.canTransitionTo(UpdateStatus.checking), true);
      expect(UpdateStatus.none.canTransitionTo(UpdateStatus.available), false);

      // From checking
      expect(UpdateStatus.checking.canTransitionTo(UpdateStatus.none), true);
      expect(UpdateStatus.checking.canTransitionTo(UpdateStatus.available), true);
      expect(UpdateStatus.checking.canTransitionTo(UpdateStatus.failed), true);
      expect(UpdateStatus.checking.canTransitionTo(UpdateStatus.downloading), false);

      // From available
      expect(UpdateStatus.available.canTransitionTo(UpdateStatus.downloading), true);
      expect(UpdateStatus.available.canTransitionTo(UpdateStatus.skipped), true);
      expect(UpdateStatus.available.canTransitionTo(UpdateStatus.cancelled), true);
      expect(UpdateStatus.available.canTransitionTo(UpdateStatus.installing), false);

      // From downloading
      expect(UpdateStatus.downloading.canTransitionTo(UpdateStatus.downloaded), true);
      expect(UpdateStatus.downloading.canTransitionTo(UpdateStatus.failed), true);
      expect(UpdateStatus.downloading.canTransitionTo(UpdateStatus.cancelled), true);
      expect(UpdateStatus.downloading.canTransitionTo(UpdateStatus.installing), false);

      // From downloaded
      expect(UpdateStatus.downloaded.canTransitionTo(UpdateStatus.installing), true);
      expect(UpdateStatus.downloaded.canTransitionTo(UpdateStatus.failed), true);
      expect(UpdateStatus.downloaded.canTransitionTo(UpdateStatus.available), false);

      // From installing
      expect(UpdateStatus.installing.canTransitionTo(UpdateStatus.installed), true);
      expect(UpdateStatus.installing.canTransitionTo(UpdateStatus.failed), true);
      expect(UpdateStatus.installing.canTransitionTo(UpdateStatus.cancelled), false);
    });

    test('should serialize and deserialize correctly', () {
      for (final status in UpdateStatus.values) {
        final json = status.toJson();
        final deserialized = UpdateStatus.fromJson(json);
        expect(deserialized, status);
      }
    });

    test('should handle invalid JSON gracefully', () {
      expect(UpdateStatus.fromJson('invalid'), UpdateStatus.none);
      expect(UpdateStatus.fromString('invalid'), UpdateStatus.none);
    });
  });

  group('UpdateInfo Tests', () {
    test('should create valid UpdateInfo instance', () {
      final updateInfo = UpdateInfo(
        updateAvailable: true,
        version: '1.2.0',
        downloadUrl: 'https://example.com/update.exe',
        hash: 'abc123',
        hashType: 'SHA-256',
        releaseNotes: {'en': 'Bug fixes', 'zh': '错误修复'},
        forceUpdate: false,
        status: UpdateStatus.available,
      );

      expect(updateInfo.updateAvailable, true);
      expect(updateInfo.version, '1.2.0');
      expect(updateInfo.downloadUrl, 'https://example.com/update.exe');
      expect(updateInfo.hash, 'abc123');
      expect(updateInfo.hashType, 'SHA-256');
      expect(updateInfo.forceUpdate, false);
      expect(updateInfo.status, UpdateStatus.available);
    });

    test('should validate update info correctly', () {
      // Valid update info
      final validInfo = UpdateInfo(
        updateAvailable: true,
        version: '1.2.0',
        downloadUrl: 'https://example.com/update.exe',
        hash: 'abc123',
        hashType: 'SHA-256',
      );
      expect(validInfo.isValid, true);

      // No update available (always valid)
      final noUpdateInfo = UpdateInfo(updateAvailable: false);
      expect(noUpdateInfo.isValid, true);

      // Invalid update info (missing required fields)
      final invalidInfo = UpdateInfo(
        updateAvailable: true,
        version: '1.2.0',
        // Missing downloadUrl, hash, hashType
      );
      expect(invalidInfo.isValid, false);
    });

    test('should check download capability correctly', () {
      final availableInfo = UpdateInfo(
        updateAvailable: true,
        version: '1.2.0',
        downloadUrl: 'https://example.com/update.exe',
        hash: 'abc123',
        hashType: 'SHA-256',
        status: UpdateStatus.available,
      );
      expect(availableInfo.canDownload, true);

      final downloadingInfo = availableInfo.copyWith(status: UpdateStatus.downloading);
      expect(downloadingInfo.canDownload, false);

      final failedInfo = availableInfo.copyWith(status: UpdateStatus.failed);
      expect(failedInfo.canDownload, true);
    });

    test('should check install capability correctly', () {
      final downloadedInfo = UpdateInfo(
        updateAvailable: true,
        version: '1.2.0',
        status: UpdateStatus.downloaded,
        localFilePath: '/path/to/update.exe',
      );
      expect(downloadedInfo.canInstall, true);

      final availableInfo = downloadedInfo.copyWith(status: UpdateStatus.available);
      expect(availableInfo.canInstall, false);

      final noFileInfo = UpdateInfo(
        updateAvailable: true,
        version: '1.2.0',
        status: UpdateStatus.downloaded,
        localFilePath: null, // 明确设置为null
      );
      expect(noFileInfo.canInstall, false);
    });

    test('should get release notes by language', () {
      final updateInfo = UpdateInfo(
        updateAvailable: true,
        releaseNotes: {
          'en': 'English notes',
          'zh': 'Chinese notes',
          'fr': 'French notes',
        },
      );

      expect(updateInfo.getReleaseNotes('zh'), 'Chinese notes');
      expect(updateInfo.getReleaseNotes('en'), 'English notes');
      expect(updateInfo.getReleaseNotes('fr'), 'French notes');
      
      // Fallback to English
      expect(updateInfo.getReleaseNotes('de'), 'English notes');
      
      // No release notes
      final noNotesInfo = UpdateInfo(updateAvailable: true);
      expect(noNotesInfo.getReleaseNotes('en'), '');
    });

    test('should serialize and deserialize correctly', () {
      final originalInfo = UpdateInfo(
        updateAvailable: true,
        version: '1.2.0',
        downloadUrl: 'https://example.com/update.exe',
        hash: 'abc123',
        hashType: 'SHA-256',
        releaseNotes: {'en': 'Bug fixes', 'zh': '错误修复'},
        forceUpdate: true,
        status: UpdateStatus.downloading,
        localFilePath: '/path/to/file',
        skipCount: 2,
        downloadProgress: 0.5,
        fileSize: 1024,
        errorMessage: 'Test error',
      );

      final json = originalInfo.toJson();
      final deserializedInfo = UpdateInfo.fromJson(json);

      expect(deserializedInfo.updateAvailable, originalInfo.updateAvailable);
      expect(deserializedInfo.version, originalInfo.version);
      expect(deserializedInfo.downloadUrl, originalInfo.downloadUrl);
      expect(deserializedInfo.hash, originalInfo.hash);
      expect(deserializedInfo.hashType, originalInfo.hashType);
      expect(deserializedInfo.forceUpdate, originalInfo.forceUpdate);
      expect(deserializedInfo.status, originalInfo.status);
      expect(deserializedInfo.localFilePath, originalInfo.localFilePath);
      expect(deserializedInfo.skipCount, originalInfo.skipCount);
      expect(deserializedInfo.downloadProgress, originalInfo.downloadProgress);
      expect(deserializedInfo.fileSize, originalInfo.fileSize);
      expect(deserializedInfo.errorMessage, originalInfo.errorMessage);
    });

    test('should create noUpdate instance correctly', () {
      final noUpdateInfo = UpdateInfo.noUpdate();
      expect(noUpdateInfo.updateAvailable, false);
      expect(noUpdateInfo.status, UpdateStatus.none);
    });

    test('should copy with new values correctly', () {
      final originalInfo = UpdateInfo(
        updateAvailable: true,
        version: '1.0.0',
        status: UpdateStatus.available,
      );

      final copiedInfo = originalInfo.copyWith(
        version: '1.1.0',
        status: UpdateStatus.downloading,
        downloadProgress: 0.3,
      );

      expect(copiedInfo.updateAvailable, true); // Unchanged
      expect(copiedInfo.version, '1.1.0'); // Changed
      expect(copiedInfo.status, UpdateStatus.downloading); // Changed
      expect(copiedInfo.downloadProgress, 0.3); // Changed
    });
  });

  group('UpdateConfig Tests', () {
    test('should create valid UpdateConfig instance', () {
      final config = UpdateConfig(
        serverUrl: 'https://api.example.com',
        checkInterval: const Duration(hours: 2),
        autoDownload: false,
        wifiOnly: false,
        maxRetries: 5,
        timeout: const Duration(minutes: 10),
        skipRemindHours: 2,
      );

      expect(config.serverUrl, 'https://api.example.com');
      expect(config.checkInterval, const Duration(hours: 2));
      expect(config.autoDownload, false);
      expect(config.wifiOnly, false);
      expect(config.maxRetries, 5);
      expect(config.timeout, const Duration(minutes: 10));
      expect(config.skipRemindHours, 2);
    });

    test('should validate configuration correctly', () {
      // Valid config
      final validConfig = UpdateConfig(
        serverUrl: 'https://api.example.com',
        maxRetries: 3,
        skipRemindHours: 1,
        startupCheckDelay: 5,
        downloadConcurrency: 1,
        downloadBufferSize: 8192,
      );
      expect(validConfig.isValid, true);

      // Invalid server URL
      final invalidUrlConfig = UpdateConfig(serverUrl: '');
      expect(invalidUrlConfig.isValid, false);

      final invalidSchemeConfig = UpdateConfig(serverUrl: 'ftp://example.com');
      expect(invalidSchemeConfig.isValid, false);

      // Invalid numeric values
      final invalidRetriesConfig = UpdateConfig(
        serverUrl: 'https://api.example.com',
        maxRetries: -1,
      );
      expect(invalidRetriesConfig.isValid, false);

      final invalidRemindHoursConfig = UpdateConfig(
        serverUrl: 'https://api.example.com',
        skipRemindHours: 200, // > 168 hours (1 week)
      );
      expect(invalidRemindHoursConfig.isValid, false);
    });

    test('should serialize and deserialize correctly', () {
      final originalConfig = UpdateConfig(
        serverUrl: 'https://api.example.com',
        checkInterval: const Duration(hours: 3),
        autoDownload: false,
        wifiOnly: false,
        maxRetries: 5,
        timeout: const Duration(minutes: 8),
        skipRemindHours: 3,
        enableAutoCheck: false,
        checkOnConnect: false,
        checkOnStartup: false,
        startupCheckDelay: 10,
        enableNotifications: false,
        downloadConcurrency: 2,
        downloadBufferSize: 16384,
      );

      final json = originalConfig.toJson();
      final deserializedConfig = UpdateConfig.fromJson(json);

      expect(deserializedConfig.serverUrl, originalConfig.serverUrl);
      expect(deserializedConfig.checkInterval, originalConfig.checkInterval);
      expect(deserializedConfig.autoDownload, originalConfig.autoDownload);
      expect(deserializedConfig.wifiOnly, originalConfig.wifiOnly);
      expect(deserializedConfig.maxRetries, originalConfig.maxRetries);
      expect(deserializedConfig.timeout, originalConfig.timeout);
      expect(deserializedConfig.skipRemindHours, originalConfig.skipRemindHours);
      expect(deserializedConfig.enableAutoCheck, originalConfig.enableAutoCheck);
      expect(deserializedConfig.checkOnConnect, originalConfig.checkOnConnect);
      expect(deserializedConfig.checkOnStartup, originalConfig.checkOnStartup);
      expect(deserializedConfig.startupCheckDelay, originalConfig.startupCheckDelay);
      expect(deserializedConfig.enableNotifications, originalConfig.enableNotifications);
      expect(deserializedConfig.downloadConcurrency, originalConfig.downloadConcurrency);
      expect(deserializedConfig.downloadBufferSize, originalConfig.downloadBufferSize);
    });

    test('should create default config correctly', () {
      final defaultConfig = UpdateConfig.defaultConfig();
      expect(defaultConfig.serverUrl, 'https://uctest.unisase.cn:9000');
      expect(defaultConfig.checkInterval, const Duration(hours: 1));
      expect(defaultConfig.autoDownload, true);
      expect(defaultConfig.wifiOnly, false); // 允许移动网络下载
      expect(defaultConfig.maxRetries, 3);
      expect(defaultConfig.timeout, const Duration(minutes: 10)); // 增加到10分钟
      expect(defaultConfig.skipRemindHours, 1);
      expect(defaultConfig.enableAutoCheck, true);
      expect(defaultConfig.checkOnConnect, true); // 启用VPN连接时检查
      expect(defaultConfig.checkOnStartup, true);
      expect(defaultConfig.startupCheckDelay, 5);
      expect(defaultConfig.enableNotifications, true);
      expect(defaultConfig.downloadConcurrency, 1);
      expect(defaultConfig.downloadBufferSize, 8192);

      final configWithUrl = UpdateConfig.defaultConfig(serverUrl: 'https://api.example.com');
      expect(configWithUrl.serverUrl, 'https://api.example.com');
    });

    test('should copy with new values correctly', () {
      final originalConfig = UpdateConfig.defaultConfig();
      final copiedConfig = originalConfig.copyWith(
        serverUrl: 'https://new-api.example.com',
        autoDownload: false,
        maxRetries: 5,
      );

      expect(copiedConfig.serverUrl, 'https://new-api.example.com'); // Changed
      expect(copiedConfig.autoDownload, false); // Changed
      expect(copiedConfig.maxRetries, 5); // Changed
      expect(copiedConfig.wifiOnly, true); // Unchanged
      expect(copiedConfig.timeout, const Duration(minutes: 5)); // Unchanged
    });
  });
}
